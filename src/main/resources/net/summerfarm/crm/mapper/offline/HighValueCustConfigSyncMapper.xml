<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.crm.mapper.offline.HighValueCustConfigSyncMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.crm.model.domain.BDVisit.HighValueCustConfigSync">
    <!--@mbg.generated-->
    <!--@Table crm_high_value_cust_config_sync-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="bd_id" jdbcType="BIGINT" property="bdId" />
    <result column="bd_region" jdbcType="VARCHAR" property="bdRegion" />
    <result column="bd_work_zone" jdbcType="VARCHAR" property="bdWorkZone" />
    <result column="gmv_threshold" jdbcType="DECIMAL" property="gmvThreshold" />
    <result column="spu_cnt_threshold" jdbcType="INTEGER" property="spuCntThreshold" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, create_time, update_time, bd_id, bd_region, bd_work_zone, gmv_threshold, 
    spu_cnt_threshold
  </sql>
</mapper>