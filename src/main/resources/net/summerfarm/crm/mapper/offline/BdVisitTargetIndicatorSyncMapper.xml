<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.crm.mapper.offline.BDVisit.BdVisitTargetIndicatorSyncMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.crm.model.domain.BDVisit.BdVisitTargetIndicatorSync">
    <!--@mbg.generated-->
    <!--@Table crm_bd_visit_target_indicator_sync-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="bd_visit_target_id" jdbcType="BIGINT" property="bdVisitTargetId" />
    <result column="bd_visit_target_indicator_id" jdbcType="BIGINT" property="bdVisitTargetIndicatorId" />
    <result column="bd_id" jdbcType="INTEGER" property="bdId" />
    <result column="target_date" jdbcType="DATE" property="targetDate" />
    <result column="indicator_current_value" jdbcType="DECIMAL" property="indicatorCurrentValue" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, create_time, update_time, bd_visit_target_id, bd_visit_target_indicator_id, bd_id, 
    target_date, indicator_current_value
  </sql>
</mapper>