<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.crm.mapper.manage.BDVisit.BdTargetIndicatorConfigMapper">
  
  <resultMap id="BaseResultMap" type="net.summerfarm.crm.model.domain.BDVisit.BdTargetIndicatorConfig">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="indicator_type" jdbcType="INTEGER" property="indicatorType" />
    <result column="indicator_name" jdbcType="VARCHAR" property="indicatorName" />
    <result column="business_type_config" jdbcType="VARCHAR" property="businessTypeConfig" />
    <result column="metrics_method_config" jdbcType="VARCHAR" property="metricsMethodConfig" />
    <result column="category_name_config" jdbcType="VARCHAR" property="categoryNameConfig" />
  </resultMap>

  <sql id="Base_Column_List">
    id, create_time, update_time, indicator_type, indicator_name, business_type_config, metrics_method_config, category_name_config
  </sql>

  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from crm_bd_target_indicator_config
    where id = #{id,jdbcType=BIGINT}
  </select>

  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from crm_bd_target_indicator_config
    where id = #{id,jdbcType=BIGINT}
  </delete>

  <insert id="insertBatch" parameterType="java.util.List" useGeneratedKeys="true" keyProperty="id">
    insert into crm_bd_target_indicator_config (indicator_type, indicator_name, business_type_config, metrics_method_config, category_name_config)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.indicatorType,jdbcType=INTEGER}, #{item.indicatorName,jdbcType=VARCHAR}, #{item.businessTypeConfig,jdbcType=VARCHAR},
      #{item.metricsMethodConfig,jdbcType=VARCHAR}, #{item.categoryNameConfig,jdbcType=VARCHAR})
    </foreach>
  </insert>

  <insert id="insertSelective" parameterType="net.summerfarm.crm.model.domain.BDVisit.BdTargetIndicatorConfig" useGeneratedKeys="true" keyProperty="id">
    insert into crm_bd_target_indicator_config
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="indicatorType != null">
        indicator_type,
      </if>
      <if test="indicatorName != null">
        indicator_name,
      </if>
      <if test="businessTypeConfig != null">
        business_type_config,
      </if>
      <if test="metricsMethodConfig != null">
        metrics_method_config,
      </if>
      <if test="categoryNameConfig != null">
        category_name_config,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="indicatorType != null">
        #{indicatorType,jdbcType=INTEGER},
      </if>
      <if test="indicatorName != null">
        #{indicatorName,jdbcType=VARCHAR},
      </if>
      <if test="businessTypeConfig != null">
        #{businessTypeConfig,jdbcType=VARCHAR},
      </if>
      <if test="metricsMethodConfig != null">
        #{metricsMethodConfig,jdbcType=VARCHAR},
      </if>
      <if test="categoryNameConfig != null">
        #{categoryNameConfig,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>

  <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.crm.model.entity.BDVisit.BdTargetIndicatorConfigUpdate">
    update crm_bd_target_indicator_config
    <set>
      <if test="indicatorType != null">
        indicator_type = #{indicatorType,jdbcType=INTEGER},
      </if>
      <if test="indicatorName != null">
        indicator_name = #{indicatorName,jdbcType=VARCHAR},
      </if>
      <if test="businessTypeConfig != null">
        business_type_config = #{businessTypeConfig,jdbcType=VARCHAR},
      </if>
      <if test="metricsMethodConfig != null">
        metrics_method_config = #{metricsMethodConfig,jdbcType=VARCHAR},
      </if>
      <if test="categoryNameConfig != null">
        category_name_config = #{categoryNameConfig,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="list" parameterType="net.summerfarm.crm.model.entity.BDVisit.BdTargetIndicatorConfigQuery" resultMap="BaseResultMap">
    SELECT
    <include refid="Base_Column_List" />
    FROM crm_bd_target_indicator_config
    <where>
      <if test="id != null">
        AND id = #{id,jdbcType=BIGINT}
      </if>
      <if test="indicatorType != null">
        AND indicator_type = #{indicatorType,jdbcType=INTEGER}
      </if>
      <if test="indicatorName != null and indicatorName != ''">
        AND indicator_name = #{indicatorName,jdbcType=VARCHAR}
      </if>
      <if test="businessTypeConfig != null and businessTypeConfig != ''">
        AND business_type_config = #{businessTypeConfig,jdbcType=VARCHAR}
      </if>
      <if test="metricsMethodConfig != null and metricsMethodConfig != ''">
        AND metrics_method_config = #{metricsMethodConfig,jdbcType=VARCHAR}
      </if>
      <if test="categoryNameConfig != null and categoryNameConfig != ''">
        AND category_name_config = #{categoryNameConfig,jdbcType=VARCHAR}
      </if>
      <if test="createTime != null">
        AND create_time = #{createTime,jdbcType=TIMESTAMP}
      </if>
      <if test="updateTime != null">
        AND update_time = #{updateTime,jdbcType=TIMESTAMP}
      </if>
    </where>
    ORDER BY id DESC
  </select>

</mapper>
