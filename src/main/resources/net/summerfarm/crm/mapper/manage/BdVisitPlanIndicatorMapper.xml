<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.crm.mapper.manage.BDVisit.BdVisitPlanIndicatorMapper">
  
  <resultMap id="BaseResultMap" type="net.summerfarm.crm.model.domain.BDVisit.BdVisitPlanIndicator">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="visit_plan_id" jdbcType="BIGINT" property="visitPlanId" />
    <result column="sales_id" jdbcType="BIGINT" property="salesId" />
    <result column="indicator_type" jdbcType="VARCHAR" property="indicatorType" />
    <result column="indicator_name" jdbcType="VARCHAR" property="indicatorName" />
    <result column="plan_date" jdbcType="DATE" property="planDate" />
    <result column="plan_value" jdbcType="DECIMAL" property="planValue" />
    <result column="actual_value" jdbcType="DECIMAL" property="actualValue" />
    <result column="indicator_status" jdbcType="VARCHAR" property="indicatorStatus" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>

  <sql id="Base_Column_List">
    id, visit_plan_id, sales_id, indicator_type, indicator_name, plan_date, plan_value, actual_value, indicator_status, creator, create_time, update_time
  </sql>

  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from crm_bd_visit_plan_indicator
    where id = #{id,jdbcType=BIGINT}
  </select>

  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from crm_bd_visit_plan_indicator
    where id = #{id,jdbcType=BIGINT}
  </delete>

  <insert id="insertBatch" parameterType="java.util.List" useGeneratedKeys="true" keyProperty="id">
    insert into crm_bd_visit_plan_indicator (visit_plan_id, sales_id, indicator_type,
    indicator_name, plan_date, plan_value, actual_value, indicator_status, creator)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.visitPlanId,jdbcType=BIGINT}, #{item.salesId,jdbcType=BIGINT}, #{item.indicatorType,jdbcType=VARCHAR},
      #{item.indicatorName,jdbcType=VARCHAR}, #{item.planDate,jdbcType=DATE}, #{item.planValue,jdbcType=DECIMAL}, #{item.actualValue,jdbcType=DECIMAL}, #{item.indicatorStatus,jdbcType=VARCHAR}, #{item.creator,jdbcType=VARCHAR})
    </foreach>
  </insert>

  <insert id="insertSelective" parameterType="net.summerfarm.crm.model.domain.BDVisit.BdVisitPlanIndicator" useGeneratedKeys="true" keyProperty="id">
    insert into crm_bd_visit_plan_indicator
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="visitPlanId != null">
        visit_plan_id,
      </if>
      <if test="salesId != null">
        sales_id,
      </if>
      <if test="indicatorType != null">
        indicator_type,
      </if>
      <if test="indicatorName != null">
        indicator_name,
      </if>
      <if test="planDate != null">
        plan_date,
      </if>
      <if test="planValue != null">
        plan_value,
      </if>
      <if test="actualValue != null">
        actual_value,
      </if>
      <if test="indicatorStatus != null">
        indicator_status,
      </if>
      <if test="creator != null">
        creator,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="visitPlanId != null">
        #{visitPlanId,jdbcType=BIGINT},
      </if>
      <if test="salesId != null">
        #{salesId,jdbcType=BIGINT},
      </if>
      <if test="indicatorType != null">
        #{indicatorType,jdbcType=VARCHAR},
      </if>
      <if test="indicatorName != null">
        #{indicatorName,jdbcType=VARCHAR},
      </if>
      <if test="planDate != null">
        #{planDate,jdbcType=DATE},
      </if>
      <if test="planValue != null">
        #{planValue,jdbcType=DECIMAL},
      </if>
      <if test="actualValue != null">
        #{actualValue,jdbcType=DECIMAL},
      </if>
      <if test="indicatorStatus != null">
        #{indicatorStatus,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>

  <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.crm.model.entity.BDVisit.BdVisitPlanIndicatorUpdate">
    update crm_bd_visit_plan_indicator
    <set>
      <if test="visitPlanId != null">
        visit_plan_id = #{visitPlanId,jdbcType=BIGINT},
      </if>
      <if test="salesId != null">
        sales_id = #{salesId,jdbcType=BIGINT},
      </if>
      <if test="indicatorType != null">
        indicator_type = #{indicatorType,jdbcType=VARCHAR},
      </if>
      <if test="indicatorName != null">
        indicator_name = #{indicatorName,jdbcType=VARCHAR},
      </if>
      <if test="planDate != null">
        plan_date = #{planDate,jdbcType=DATE},
      </if>
      <if test="planValue != null">
        plan_value = #{planValue,jdbcType=DECIMAL},
      </if>
      <if test="actualValue != null">
        actual_value = #{actualValue,jdbcType=DECIMAL},
      </if>
      <if test="indicatorStatus != null">
        indicator_status = #{indicatorStatus,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>

  <!-- 根据查询条件查询记录列表 -->
  <select id="list" parameterType="net.summerfarm.crm.model.entity.BDVisit.BdVisitPlanIndicatorQuery" resultMap="BaseResultMap">
    SELECT
    <include refid="Base_Column_List" />
    FROM crm_bd_visit_plan_indicator
    <where>
      <if test="id != null">
        AND id = #{id,jdbcType=BIGINT}
      </if>
      <if test="visitPlanId != null">
        AND visit_plan_id = #{visitPlanId,jdbcType=BIGINT}
      </if>
      <if test="salesId != null">
        AND sales_id = #{salesId,jdbcType=BIGINT}
      </if>
      <if test="indicatorType != null and indicatorType != ''">
        AND indicator_type = #{indicatorType,jdbcType=VARCHAR}
      </if>
      <if test="indicatorName != null and indicatorName != ''">
        AND indicator_name = #{indicatorName,jdbcType=VARCHAR}
      </if>
      <if test="planDate != null">
        AND plan_date = #{planDate,jdbcType=DATE}
      </if>
      <if test="planValue != null">
        AND plan_value = #{planValue,jdbcType=DECIMAL}
      </if>
      <if test="actualValue != null">
        AND actual_value = #{actualValue,jdbcType=DECIMAL}
      </if>
      <if test="indicatorStatus != null and indicatorStatus != ''">
        AND indicator_status = #{indicatorStatus,jdbcType=VARCHAR}
      </if>
      <if test="creator != null and creator != ''">
        AND creator = #{creator,jdbcType=VARCHAR}
      </if>
      <if test="createTime != null">
        AND create_time = #{createTime,jdbcType=TIMESTAMP}
      </if>
      <if test="updateTime != null">
        AND update_time = #{updateTime,jdbcType=TIMESTAMP}
      </if>
    </where>
    ORDER BY id DESC
  </select>

  <!-- 批量更新指标当前值和潜力值 -->
  <update id="batchUpdateIndicatorValueAndPotential">
    <foreach collection="list" item="item" separator=";">
      UPDATE crm_bd_visit_plan_indicator 
      SET 
        <if test="item.indicatorCurrentValue != null">
          indicator_current_value = #{item.indicatorCurrentValue,jdbcType=DECIMAL},
        </if>
        <if test="item.indicatorPotentialValue != null">
          indicator_potential_value = #{item.indicatorPotentialValue,jdbcType=DECIMAL},
        </if>
        update_time = NOW()
      WHERE bd_visit_plan_id = #{item.bdVisitPlanId,jdbcType=BIGINT}
        AND bd_visit_target_indicator_id = #{item.bdVisitTargetIndicatorId,jdbcType=BIGINT}
        AND m_id = #{item.mId,jdbcType=BIGINT}
    </foreach>
  </update>

  <!-- 根据门店ID批量更新潜力值 -->
  <update id="batchUpdatePotentialValueByMerchantId">
    <foreach collection="list" item="item" separator=";">
      UPDATE crm_bd_visit_plan_indicator 
      SET 
        indicator_potential_value = #{item.indicatorPotentialValue,jdbcType=DECIMAL},
        update_time = NOW()
      WHERE m_id = #{item.mId,jdbcType=BIGINT}
    </foreach>
  </update>

  <!-- 根据门店ID查询拜访计划指标 -->
  <select id="selectByMerchantIds" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from crm_bd_visit_plan_indicator
    where m_id in
    <foreach collection="merchantIds" item="merchantId" open="(" close=")" separator=",">
      #{merchantId,jdbcType=BIGINT}
    </foreach>
    order by id desc
  </select>

  <!-- 根据销售ID查询拜访计划指标 -->
  <select id="selectBySalesId" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from crm_bd_visit_plan_indicator
    where bd_id = #{salesId,jdbcType=INTEGER}
    order by id desc
  </select>

  <!-- 根据目标指标ID列表查询拜访计划指标 -->
  <select id="selectByTargetIndicatorIds" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from crm_bd_visit_plan_indicator
    where bd_visit_target_indicator_id in
    <foreach collection="targetIndicatorIds" item="targetIndicatorId" open="(" close=")" separator=",">
      #{targetIndicatorId,jdbcType=BIGINT}
    </foreach>
    order by id desc
  </select>

</mapper>