<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.summerfarm.crm.mapper.manage.FollowUpRecordMapper" >
  <resultMap id="BaseResultMap" type="net.summerfarm.crm.model.domain.FollowUpRecord" >
    <id column="id" property="id" jdbcType="INTEGER" />
    <result column="m_id" property="mId" jdbcType="BIGINT" />
    <result column="admin_id" property="adminId" jdbcType="BIGINT" />
    <result column="admin_name" property="adminName" jdbcType="VARCHAR" />
    <result column="creator" property="creator" jdbcType="VARCHAR" />
    <result column="m_lifecycle" property="mLifecycle" jdbcType="INTEGER" />
    <result column="m_tag" property="mTag" jdbcType="VARCHAR" />
    <result column="m_last_order_time" property="mLastOrderTime" jdbcType="TIMESTAMP" />
    <result column="follow_up_way" property="followUpWay" jdbcType="VARCHAR" />
    <result column="follow_up_pic" property="followUpPic" jdbcType="VARCHAR" />
    <result column="status" property="status" jdbcType="INTEGER" />
    <result column="priority" property="priority" jdbcType="INTEGER" />
    <result column="condition" property="condition" jdbcType="VARCHAR" />
    <result column="add_time" property="addTime" jdbcType="TIMESTAMP" />
    <result column="contact_id" property="contactId"  />
    <result column="next_follow_time" property="nextFollowTime"/>
    <result column="expected_content" property="expectedContent"/>
    <result column="visit_objective" property="visitObjective"/>
    <result column="whether_remark" property="whetherRemark"/>
    <result column="visit_type" property="visitType"/>
    <result column="location" property="location"/>
    <result column="kp_id" property="kpId"/>
    <result column="poi_note" property="poiNote"/>
    <result column="escort_admin_id" property="escortAdminId"/>
    <result column="account_id" property="accountId"/>
    <result column="feedback" property="feedback"/>
    <result column="feedback_time" property="feedbackTime"/>
    <result column="visit_plan_id" property="visitPlanId"/>
    <result column="escort_visit_plan_id" property="escortVisitPlanId"/>
    <result column="poi_update_flag" property="poiUpdateFlag"/>
    <result column="last_visit_time" property="lastVisitTime" jdbcType="TIMESTAMP"/>
    <result column="last_visit_record_id" property="lastVisitRecordId" jdbcType="INTEGER"/>
  </resultMap>
  <sql id="Base_Column_List">
    id, m_id, admin_id, admin_name, creator, m_lifecycle, m_tag, m_last_order_time, follow_up_way, follow_up_pic, status,
    priority, `condition`, add_time,contact_id,next_follow_time,expected_content,visit_objective,whether_remark,visit_type,location,
    kp_id,poi_note,escort_admin_id,account_id,feedback,feedback_time,visit_plan_id,escort_visit_plan_id,poi_update_flag,
    last_visit_time, last_visit_record_id
  </sql>

  <select id="selectVO" parameterType="net.summerfarm.crm.model.vo.FollowUpRecordVO"
          resultType="net.summerfarm.crm.model.vo.FollowUpRecordVO">
    SELECT t.id,
    t.m_id mId,
    t.admin_id adminId,
    t.admin_name adminName,
    t.follow_up_way followUpWay,
    t.follow_up_pic followUpPic,
    t.status,
    t.condition,
    t.add_time addTime,
    t.creator creator,
    t.location location,
    t.kp_id kpId,
    t.visit_objective visitObjective,
    t.visit_type visitType,
    t.escort_admin_id escortAdminId,
    t.visit_plan_id visitPlanId,
    t.escort_visit_plan_id escortVisitPlanId,
    t.account_id accountId,
    t.feedback,
    t.poi_update_flag poiUpdateFlag,
    t.door_pic doorPic,
    t.contact_id contactId,
    t.last_visit_time lastVisitTime,
    t.last_visit_record_id lastVisitRecordId
    FROM follow_up_record t join merchant m on t.m_id = m.m_id
    <where>
      <if test="adminId != null">
        AND t.admin_id = #{adminId}
      </if>
      <if test="contactId != null">
        AND t.contact_id = #{contactId}
      </if>
      <if test="addTime != null">
        AND date(t.add_time) = date(#{addTime})
      </if>
      <if test="startTime !=null">
        AND t.add_time <![CDATA[>=]]>  #{startTime}
      </if>
      <if test="endTime !=null">
        AND t.add_time <![CDATA[<]]>  #{endTime}
      </if>
      <if test="mId != null">
        AND t.m_id = #{mId}
      </if>
      <if test="areaNo != null">
        AND t.area_no = #{areaNo}
      </if>
      <if test="id != null">
        AND t.id = #{id}
      </if>
      <if test="visitType != null">
        AND t.visit_type = #{visitType}
      </if>
      <if test="mId != null">
        AND t.m_id = #{mId}
      </if>
      <if test="followUpWayList != null and followUpWayList.size() >0">
        AND t.follow_up_way in
        <foreach collection="followUpWayList" item="item" open="(" close=")" separator=",">
          #{item}
        </foreach>
      </if>
      <if test="followUpWay != null and followUpWay!=''">
        AND t.follow_up_way =#{followUpWay}
      </if>
      <if test="areaNos != null and areaNos.size() > 0">
        AND t.area_no in
        <foreach collection="areaNos" item="item" open="(" close=")" separator=",">
          #{item}
        </foreach>
      </if>
      <if test="salesCityList != null and salesCityList.size > 0">
        AND (t.province, t.city, t.area) in
        <foreach collection="salesCityList" item="item" open="(" close=")" separator=",">
          (#{item.province}, #{item.city}, #{item.area})
        </foreach>
      </if>
      <if test="province != null">
        AND t.province = #{province}
      </if>
      <if test="city != null">
        AND t.city = #{city}
      </if>
      <if test="area != null">
        AND t.area = #{area}
      </if>
      <if test="mname != null and mname != ''">
        and m.mname like concat('%', #{mname}, '%')
      </if>
    </where>
    ORDER BY t.add_time desc
  </select>

  <update id="updateById" parameterType="net.summerfarm.crm.model.domain.FollowUpRecord">
    UPDATE follow_up_record t
    <set>
      <if test="followUpPic != null" >
        t.follow_up_pic = #{followUpPic},
      </if>
      <if test="followUpWay != null" >
        t.follow_up_way = #{followUpWay},
      </if>
      <if test="status != null" >
        t.status =#{status},
      </if>
      <if test="condition != null" >
        t.condition = #{condition,jdbcType=VARCHAR},
      </if>
      <if test=" addTime !=null">
        t.add_time = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="expectedContent != null">
        t.expected_content = #{expectedContent},
      </if>
      <if test="visitObjective != null">
          t.visit_objective = #{visitObjective},
      </if>
      <if test="feedback != null">
        t.feedback = #{feedback},
      </if>
      <if test="feedbackTime != null">
        t.feedback_time = #{feedbackTime}
      </if>
      <if test="poiUpdateFlag != null">
        t.poi_update_flag = #{poiUpdateFlag}
      </if>
    </set>
    where t.id = #{id,jdbcType=INTEGER}
  </update>

  <select id="select" resultMap="BaseResultMap" parameterType="net.summerfarm.crm.model.domain.FollowUpRecord" >
    select
    <include refid="Base_Column_List" />
    from follow_up_record
    <where>
      <if test="priority != null" >
        AND priority = #{priority}
      </if>
      <if test="status != null" >
        AND status = #{status}
      </if>
      <if test="mId != null">
        AND  m_id =#{mId}
      </if>
    </where>
  </select>


  <select id="selectById" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
    select
    <include refid="Base_Column_List" />
    from follow_up_record
    where id=#{id}
  </select>

  <insert id="insertSelective" useGeneratedKeys="true" keyProperty="id" parameterType="net.summerfarm.crm.model.domain.FollowUpRecord" >
    insert into follow_up_record
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="mId != null" >
        m_id,
      </if>
      <if test="adminId != null" >
        admin_id,
      </if>
      <if test="adminName != null" >
        admin_name,
      </if>
      <if test="creator != null" >
        creator,
      </if>
      <if test="mLifecycle != null" >
        m_lifecycle,
      </if>
      <if test="mTag != null" >
        m_tag,
      </if>
      <if test="mLastOrderTime != null" >
        m_last_order_time,
      </if>
      <if test="followUpPic != null" >
        follow_up_pic,
      </if>
      <if test="followUpWay != null" >
        follow_up_way,
      </if>
      <if test="status != null" >
        status,
      </if>
      <if test="condition != null" >
        `condition`,
      </if>
      <if test="addTime != null" >
        add_time,
      </if>
      <if test="priority != null" >
        priority,
      </if>
      <if test="contactId != null" >
        contact_id,
      </if>
      <if test="nextFollowTime != null">
        next_follow_time,
      </if>
      <if test="expectedContent != null">
        expected_content,
      </if>
      <if test=" visitObjective != null">
        visit_objective,
      </if>
      <if test="whetherRemark != null">
        whether_remark,
      </if>
      <if test="visitType != null">
        visit_type,
      </if>
      <if test="location != null">
        location,
      </if>
      <if test="kpId != null">
        kp_id,
      </if>
      <if test="poiNote != null">
        poi_note,
      </if>
      <if test="visitPlanId != null">
        visit_plan_id,
      </if>
      <if test="escortVisitPlanId != null">
        escort_visit_plan_id,
      </if>
      <if test="accountId != null">
        account_id,
      </if>
      <if test="doorPic != null">
        door_pic,
      </if>
      <if test="province != null">
        province,
      </if>
      <if test="city != null">
        city,
      </if>
      <if test="area != null">
        area,
      </if>
      <if test="areaNo != null">
        area_no,
      </if>
      <if test="lastVisitTime != null">
        last_visit_time,
      </if>
      <if test="lastVisitRecordId != null">
        last_visit_record_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="mId != null" >
        #{mId,jdbcType=BIGINT},
      </if>
      <if test="adminId != null" >
        #{adminId},
      </if>
      <if test="adminName != null" >
        #{adminName},
      </if>
      <if test="creator != null" >
        #{creator},
      </if>
      <if test="mLifecycle != null" >
        #{mLifecycle},
      </if>
      <if test="mTag != null" >
        #{mTag},
      </if>
      <if test="mLastOrderTime != null" >
        #{mLastOrderTime},
      </if>
      <if test="followUpPic != null" >
        #{followUpPic},
      </if>
      <if test="followUpWay != null" >
        #{followUpWay},
      </if>
      <if test="status != null" >
        #{status},
      </if>
      <if test="condition != null" >
        #{condition,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null" >
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="priority != null" >
        #{priority},
      </if>
      <if test="contactId != null" >
        #{contactId},
      </if>
      <if test="nextFollowTime != null">
        #{nextFollowTime},
      </if>
      <if test="expectedContent != null">
        #{expectedContent},
      </if>
      <if test=" visitObjective != null">
        #{visitObjective},
      </if>
      <if test="whetherRemark != null">
        #{whetherRemark},
      </if>
      <if test="visitType != null">
        #{visitType},
      </if>
      <if test="location != null">
        #{location},
      </if>
      <if test="kpId != null">
        #{kpId},
      </if>
      <if test="poiNote != null">
        #{poiNote},
      </if>
      <if test="visitPlanId != null">
        #{visitPlanId},
      </if>
      <if test="escortVisitPlanId != null">
        #{escortVisitPlanId},
      </if>
      <if test="accountId != null">
        #{accountId},
      </if>
      <if test="doorPic != null">
        #{doorPic},
      </if>
      <if test="province != null">
        #{province},
      </if>
      <if test="city != null">
        #{city},
      </if>
      <if test="area != null">
        #{area},
      </if>
      <if test="areaNo != null">
        #{areaNo},
      </if>
      <if test="lastVisitTime != null">
        #{lastVisitTime},
      </if>
      <if test="lastVisitRecordId != null">
        #{lastVisitRecordId},
      </if>
    </trim>
  </insert>
  <select id="selectByStart" resultType="net.summerfarm.crm.model.vo.FollowUpRecordVO">
    select
    fur.id ,
    fur.follow_up_way followUpWay , fur.condition,fur.m_id mId,
    fur.admin_id adminId,a.realname adminName ,fur.add_time addTime,
    fur.visit_objective visitObjective,fur.escort_admin_id escortAdminId,fur.location location,fur.kp_id kpId,
    fur.visit_plan_id        visitPlanId,
    fur.escort_visit_plan_id escortVisitPlanId
    from follow_up_record fur
    left join admin a on a.admin_id= fur.admin_id
    WHERE visit_type = 0
    <if test="adminId != null">
      AND fur.admin_id = #{adminId}
    </if>
    <if test="startTime !=null">
      AND fur.add_time <![CDATA[>=]]>  #{startTime}
    </if>
    <if test="endTime !=null">
      AND fur.add_time <![CDATA[<]]>  #{endTime}
    </if>
    <if test="mId != null">
      AND fur.m_id = #{mId}
    </if>
    <if test="areaNo != null">
      AND fur.area_no = #{areaNo}
    </if>
    <if test="followUpWayList != null and followUpWayList.size() >0">
      AND fur.follow_up_way in
      <foreach collection="followUpWayList" item="item" open="(" close=")" separator=",">
        #{item}
      </foreach>
    </if>
    <if test="areaNos != null and areaNos.size() > 0">
      AND fur.area_no in
      <foreach collection="areaNos" item="item" open="(" close=")" separator=",">
        #{item}
      </foreach>
    </if>
    <if test="salesCityList != null and salesCityList.size > 0">
      AND (fur.province, fur.city, fur.area) in
      <foreach collection="salesCityList" item="item" open="(" close=")" separator=",">
        (#{item.province}, #{item.city}, #{item.area})
      </foreach>
    </if>
    order by fur.add_time DESC
  </select>
  <select id="noteDetails" resultMap="BaseResultMap">
    SELECT <include refid="Base_Column_List" />
    FROM follow_up_record WHERE m_id = #{mId} AND whether_remark = #{couponId} LIMIT 1
  </select>

  <select id="selectEscortRecord" resultMap="BaseResultMap">
    SELECT  <include refid="Base_Column_List" />
    FROM follow_up_record
    WHERE `status` = 1 AND visit_type = 1
    AND  escort_admin_id = #{adminId}
    and date(add_time) = #{date}
    and contact_id = #{contactId}
    and admin_id = #{escortAdminId}
  </select>
    <select id="selectByVisitPlanId" resultMap="BaseResultMap">
      SELECT  <include refid="Base_Column_List" />
      FROM follow_up_record
      <where>
        <if test="visitPlanId != null ">
          and visit_plan_id = #{visitPlanId}
        </if>
        <if test="escortVisitPlanId != null ">
          and escort_visit_plan_id = #{escortVisitPlanId}
        </if>
      </where>
    </select>

  <select id="selectLastHourRecord" resultMap="BaseResultMap">
      select
      <include refid="Base_Column_List"/>
      from follow_up_record fr
      where fr.add_time between SUBTIME(DATE_FORMAT(now(), "%Y-%m-%d %H"), '1:0:0') and SUBTIME(DATE_FORMAT(now(), "%Y-%m-%d %H"), '0:0:1')
  </select>

    <select id="selectTaskFollowUp" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from follow_up_record
        where whether_remark = #{taskId} and m_id = #{mId}
        limit 1
    </select>

  <select id="selectLatestRecord" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from follow_up_record
    where m_id = #{mId}
    and status = 1
    and visit_type = 0
    order by add_time desc
    limit 1
  </select>

  <select id="selectLastRecordIdById" resultType="java.lang.Long">
    select
    p.id
    from follow_up_record r
    left join follow_up_record p
    on r.m_id = p.m_id and r.admin_id = p.admin_id and p.id != r.id and p.add_time <![CDATA[<]]> r.add_time
    where r.id = #{id}
    and p.status = 1
    and p.visit_type = 0
    order by p.add_time desc
    limit 1
  </select>

  <!--auto generated by MybatisCodeHelper on 2024-11-26-->
  <select id="selectByFollowUpWayInAndAddTimeBetween" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from follow_up_record
    where m_id = #{mId,jdbcType=BIGINT}
    <if test="followUpWayCollection != null and followUpWayCollection.size() > 0">
    and follow_up_way in
      <foreach item="item" index="index" collection="followUpWayCollection"
               open="(" separator="," close=")">
        #{item,jdbcType=VARCHAR}
      </foreach>
    </if>
    and admin_id = #{bdId,jdbcType=INTEGER}
    and add_time <![CDATA[>]]> #{minAddTime,jdbcType=TIMESTAMP}
    and add_time <![CDATA[<]]> #{maxAddTime,jdbcType=TIMESTAMP}
  </select>

  <select id="selectLatestRecordByMidAndAdminId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from follow_up_record
    where m_id = #{mId}
    and admin_id = #{adminId}
    and visit_type = 0
    order by add_time desc
    limit 1
  </select>

  <select id="countByMIdAndAdminIdAndAddTimeAndCondition" resultType="int">
    select count(1)
    from follow_up_record
    where m_id = #{mId}
    and admin_id = #{adminId}
    and add_time = #{addTime}
    and `condition` = #{condition}
  </select>

  <select id="selectVisitedMerchantIds" resultType="java.lang.Long">
    select distinct m_id
    from follow_up_record
    where m_id in
    <foreach collection="merchantIds" item="merchantId" open="(" separator="," close=")">
      #{merchantId}
    </foreach>
    and add_time &gt;= #{startTime}
    and add_time &lt;= #{endTime}
  </select>

</mapper>