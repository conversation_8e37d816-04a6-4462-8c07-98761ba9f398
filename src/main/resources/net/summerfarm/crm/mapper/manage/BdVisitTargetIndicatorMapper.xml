<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.crm.mapper.manage.BDVisit.BdVisitTargetIndicatorMapper">
  
  <resultMap id="BaseResultMap" type="net.summerfarm.crm.model.domain.BDVisit.BdVisitTargetIndicator">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="manager_visit_target_id" jdbcType="BIGINT" property="managerVisitTargetId" />
    <result column="sales_visit_target_id" jdbcType="BIGINT" property="salesVisitTargetId" />
    <result column="sales_id" jdbcType="BIGINT" property="salesId" />
    <result column="indicator_type" jdbcType="VARCHAR" property="indicatorType" />
    <result column="indicator_name" jdbcType="VARCHAR" property="indicatorName" />
    <result column="target_date" jdbcType="DATE" property="targetDate" />
    <result column="target_value" jdbcType="DECIMAL" property="targetValue" />
    <result column="actual_value" jdbcType="DECIMAL" property="actualValue" />
    <result column="indicator_status" jdbcType="VARCHAR" property="indicatorStatus" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>

  <sql id="Base_Column_List">
    id, manager_visit_target_id, sales_visit_target_id, sales_id, indicator_type, indicator_name, target_date, target_value, actual_value, indicator_status, creator, create_time, update_time
  </sql>

  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from crm_bd_visit_target_indicator
    where id = #{id,jdbcType=BIGINT}
  </select>

  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from crm_bd_visit_target_indicator
    where id = #{id,jdbcType=BIGINT}
  </delete>

  <insert id="insertBatch" parameterType="java.util.List" useGeneratedKeys="true" keyProperty="id">
    insert into crm_bd_visit_target_indicator (manager_visit_target_id, sales_visit_target_id, sales_id,
    indicator_type, indicator_name, target_date, target_value, actual_value, indicator_status, creator)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.managerVisitTargetId,jdbcType=BIGINT}, #{item.salesVisitTargetId,jdbcType=BIGINT}, #{item.salesId,jdbcType=BIGINT},
      #{item.indicatorType,jdbcType=VARCHAR}, #{item.indicatorName,jdbcType=VARCHAR}, #{item.targetDate,jdbcType=DATE}, #{item.targetValue,jdbcType=DECIMAL}, #{item.actualValue,jdbcType=DECIMAL}, #{item.indicatorStatus,jdbcType=VARCHAR}, #{item.creator,jdbcType=VARCHAR})
    </foreach>
  </insert>

  <insert id="insertSelective" parameterType="net.summerfarm.crm.model.domain.BDVisit.BdVisitTargetIndicator" useGeneratedKeys="true" keyProperty="id">
    insert into crm_bd_visit_target_indicator
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="managerVisitTargetId != null">
        manager_visit_target_id,
      </if>
      <if test="salesVisitTargetId != null">
        sales_visit_target_id,
      </if>
      <if test="salesId != null">
        sales_id,
      </if>
      <if test="indicatorType != null">
        indicator_type,
      </if>
      <if test="indicatorName != null">
        indicator_name,
      </if>
      <if test="targetDate != null">
        target_date,
      </if>
      <if test="targetValue != null">
        target_value,
      </if>
      <if test="actualValue != null">
        actual_value,
      </if>
      <if test="indicatorStatus != null">
        indicator_status,
      </if>
      <if test="creator != null">
        creator,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="managerVisitTargetId != null">
        #{managerVisitTargetId,jdbcType=BIGINT},
      </if>
      <if test="salesVisitTargetId != null">
        #{salesVisitTargetId,jdbcType=BIGINT},
      </if>
      <if test="salesId != null">
        #{salesId,jdbcType=BIGINT},
      </if>
      <if test="indicatorType != null">
        #{indicatorType,jdbcType=VARCHAR},
      </if>
      <if test="indicatorName != null">
        #{indicatorName,jdbcType=VARCHAR},
      </if>
      <if test="targetDate != null">
        #{targetDate,jdbcType=DATE},
      </if>
      <if test="targetValue != null">
        #{targetValue,jdbcType=DECIMAL},
      </if>
      <if test="actualValue != null">
        #{actualValue,jdbcType=DECIMAL},
      </if>
      <if test="indicatorStatus != null">
        #{indicatorStatus,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>

  <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.crm.model.entity.BDVisit.BdVisitTargetIndicatorUpdate">
    update crm_bd_visit_target_indicator
    <set>
      <if test="managerVisitTargetId != null">
        manager_visit_target_id = #{managerVisitTargetId,jdbcType=BIGINT},
      </if>
      <if test="salesVisitTargetId != null">
        sales_visit_target_id = #{salesVisitTargetId,jdbcType=BIGINT},
      </if>
      <if test="salesId != null">
        sales_id = #{salesId,jdbcType=BIGINT},
      </if>
      <if test="indicatorType != null">
        indicator_type = #{indicatorType,jdbcType=VARCHAR},
      </if>
      <if test="indicatorName != null">
        indicator_name = #{indicatorName,jdbcType=VARCHAR},
      </if>
      <if test="targetDate != null">
        target_date = #{targetDate,jdbcType=DATE},
      </if>
      <if test="targetValue != null">
        target_value = #{targetValue,jdbcType=DECIMAL},
      </if>
      <if test="actualValue != null">
        actual_value = #{actualValue,jdbcType=DECIMAL},
      </if>
      <if test="indicatorStatus != null">
        indicator_status = #{indicatorStatus,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="list" parameterType="net.summerfarm.crm.model.entity.BDVisit.BdVisitTargetIndicatorQuery" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from crm_bd_visit_target_indicator
    <where>
      <if test="id != null">
        and id = #{id,jdbcType=BIGINT}
      </if>
      <if test="managerVisitTargetId != null">
        and manager_visit_target_id = #{managerVisitTargetId,jdbcType=BIGINT}
      </if>
      <if test="salesVisitTargetId != null">
        and sales_visit_target_id = #{salesVisitTargetId,jdbcType=BIGINT}
      </if>
      <if test="salesId != null">
        and sales_id = #{salesId,jdbcType=BIGINT}
      </if>
      <if test="indicatorType != null and indicatorType != ''">
        and indicator_type = #{indicatorType,jdbcType=VARCHAR}
      </if>
      <if test="indicatorName != null and indicatorName != ''">
        and indicator_name = #{indicatorName,jdbcType=VARCHAR}
      </if>
      <if test="targetDate != null">
        and target_date = #{targetDate,jdbcType=DATE}
      </if>
      <if test="targetValue != null">
        and target_value = #{targetValue,jdbcType=DECIMAL}
      </if>
      <if test="actualValue != null">
        and actual_value = #{actualValue,jdbcType=DECIMAL}
      </if>
      <if test="indicatorStatus != null and indicatorStatus != ''">
        and indicator_status = #{indicatorStatus,jdbcType=VARCHAR}
      </if>
      <if test="creator != null and creator != ''">
        and creator = #{creator,jdbcType=VARCHAR}
      </if>
      <if test="createTime != null">
        and create_time = #{createTime,jdbcType=TIMESTAMP}
      </if>
      <if test="updateTime != null">
        and update_time = #{updateTime,jdbcType=TIMESTAMP}
      </if>
    </where>
    order by id desc
  </select>

  <!-- 批量更新指标当前值和状态 -->
  <update id="batchUpdateIndicatorValueAndStatus">
    <foreach collection="list" item="item" separator=";">
      UPDATE crm_bd_visit_target_indicator 
      SET 
        actual_value = #{item.indicatorCurrentValue,jdbcType=DECIMAL},
        indicator_status = 
          CASE 
            WHEN #{item.indicatorCurrentValue,jdbcType=DECIMAL} >= (
              SELECT target_value FROM crm_bd_visit_target_indicator WHERE id = #{item.bdVisitTargetIndicatorId,jdbcType=BIGINT}
            ) THEN 1
            ELSE 0
          END,
        update_time = NOW()
      WHERE id = #{item.bdVisitTargetIndicatorId,jdbcType=BIGINT}
        AND #{item.indicatorCurrentValue,jdbcType=DECIMAL} IS NOT NULL
    </foreach>
  </update>

</mapper>