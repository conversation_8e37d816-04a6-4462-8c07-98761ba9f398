<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.crm.mapper.manage.BDVisit.CityManagerVisitTargetMapper">
  
  <resultMap id="BaseResultMap" type="net.summerfarm.crm.model.domain.BDVisit.CityManagerVisitTarget">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="target_setter_id" jdbcType="BIGINT" property="targetSetterId" />
    <result column="target_date" jdbcType="DATE" property="targetDate" />
    <result column="target_value" jdbcType="DECIMAL" property="targetValue" />
    <result column="target_unit" jdbcType="VARCHAR" property="targetUnit" />
    <result column="target_description" jdbcType="VARCHAR" property="targetDescription" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>

  <sql id="Base_Column_List">
    id, target_setter_id, target_date, target_value, target_unit, target_description, creator, create_time, update_time
  </sql>

  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from crm_city_manager_visit_target
    where id = #{id,jdbcType=BIGINT}
  </select>

  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from crm_city_manager_visit_target
    where id = #{id,jdbcType=BIGINT}
  </delete>

  <insert id="insertBatch" parameterType="java.util.List" useGeneratedKeys="true" keyProperty="id">
    insert into crm_city_manager_visit_target (target_setter_id, target_date, target_value,
    target_unit, target_description, creator)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.targetSetterId,jdbcType=BIGINT}, #{item.targetDate,jdbcType=DATE}, #{item.targetValue,jdbcType=DECIMAL},
      #{item.targetUnit,jdbcType=VARCHAR}, #{item.targetDescription,jdbcType=VARCHAR}, #{item.creator,jdbcType=VARCHAR})
    </foreach>
  </insert>

  <insert id="insertSelective" parameterType="net.summerfarm.crm.model.domain.BDVisit.CityManagerVisitTarget" useGeneratedKeys="true" keyProperty="id">
    insert into crm_city_manager_visit_target
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="targetSetterId != null">
        target_setter_id,
      </if>
      <if test="targetDate != null">
        target_date,
      </if>
      <if test="targetValue != null">
        target_value,
      </if>
      <if test="targetUnit != null">
        target_unit,
      </if>
      <if test="targetDescription != null">
        target_description,
      </if>
      <if test="creator != null">
        creator,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="targetSetterId != null">
        #{targetSetterId,jdbcType=BIGINT},
      </if>
      <if test="targetDate != null">
        #{targetDate,jdbcType=DATE},
      </if>
      <if test="targetValue != null">
        #{targetValue,jdbcType=DECIMAL},
      </if>
      <if test="targetUnit != null">
        #{targetUnit,jdbcType=VARCHAR},
      </if>
      <if test="targetDescription != null">
        #{targetDescription,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>

  <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.crm.model.entity.BDVisit.CityManagerVisitTargetUpdate">
    update crm_city_manager_visit_target
    <set>
      <if test="targetSetterId != null">
        target_setter_id = #{targetSetterId,jdbcType=BIGINT},
      </if>
      <if test="targetDate != null">
        target_date = #{targetDate,jdbcType=DATE},
      </if>
      <if test="targetValue != null">
        target_value = #{targetValue,jdbcType=DECIMAL},
      </if>
      <if test="targetUnit != null">
        target_unit = #{targetUnit,jdbcType=VARCHAR},
      </if>
      <if test="targetDescription != null">
        target_description = #{targetDescription,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="list" parameterType="net.summerfarm.crm.model.entity.BDVisit.CityManagerVisitTargetQuery" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from crm_city_manager_visit_target
    <where>
      <if test="id != null">
        and id = #{id,jdbcType=BIGINT}
      </if>
      <if test="targetSetterId != null">
        and target_setter_id = #{targetSetterId,jdbcType=BIGINT}
      </if>
      <if test="targetDate != null">
        and target_date = #{targetDate,jdbcType=DATE}
      </if>
      <if test="targetValue != null">
        and target_value = #{targetValue,jdbcType=DECIMAL}
      </if>
      <if test="targetUnit != null and targetUnit != ''">
        and target_unit = #{targetUnit,jdbcType=VARCHAR}
      </if>
      <if test="targetDescription != null and targetDescription != ''">
        and target_description = #{targetDescription,jdbcType=VARCHAR}
      </if>
      <if test="creator != null and creator != ''">
        and creator = #{creator,jdbcType=VARCHAR}
      </if>
      <if test="createTime != null">
        and create_time = #{createTime,jdbcType=TIMESTAMP}
      </if>
      <if test="updateTime != null">
        and update_time = #{updateTime,jdbcType=TIMESTAMP}
      </if>
    </where>
    order by id desc
  </select>

</mapper>