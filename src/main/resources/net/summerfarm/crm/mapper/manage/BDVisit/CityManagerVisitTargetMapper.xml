<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.crm.mapper.manage.BDVisit.CityManagerVisitTargetMapper">
  
  <resultMap id="BaseResultMap" type="net.summerfarm.crm.model.domain.BDVisit.CityManagerVisitTarget">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="target_creator" jdbcType="INTEGER" property="targetCreator" />
    <result column="target_creator_name" jdbcType="VARCHAR" property="targetCreatorName" />
    <result column="bd_name_list" jdbcType="VARCHAR" property="bdNameList" />
    <result column="target_date_range" jdbcType="VARCHAR" property="targetDateRange" />
  </resultMap>

  <sql id="Base_Column_List">
    id, create_time, update_time, target_creator, target_creator_name, bd_name_list, target_date_range
  </sql>

  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from crm_city_manager_visit_target
    where id = #{id,jdbcType=BIGINT}
  </select>

  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from crm_city_manager_visit_target
    where id = #{id,jdbcType=BIGINT}
  </delete>

  <insert id="insertBatch" parameterType="java.util.List" useGeneratedKeys="true" keyProperty="id">
    insert into crm_city_manager_visit_target (target_creator, target_creator_name, bd_name_list, target_date_range)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.targetCreator,jdbcType=INTEGER}, #{item.targetCreatorName,jdbcType=VARCHAR},
      #{item.bdNameList,jdbcType=VARCHAR}, #{item.targetDateRange,jdbcType=VARCHAR})
    </foreach>
  </insert>

  <insert id="insertSelective" parameterType="net.summerfarm.crm.model.domain.BDVisit.CityManagerVisitTarget" useGeneratedKeys="true" keyProperty="id">
    insert into crm_city_manager_visit_target
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="targetCreator != null">
        target_creator,
      </if>
      <if test="targetCreatorName != null">
        target_creator_name,
      </if>
      <if test="bdNameList != null">
        bd_name_list,
      </if>
      <if test="targetDateRange != null">
        target_date_range,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="targetCreator != null">
        #{targetCreator,jdbcType=INTEGER},
      </if>
      <if test="targetCreatorName != null">
        #{targetCreatorName,jdbcType=VARCHAR},
      </if>
      <if test="bdNameList != null">
        #{bdNameList,jdbcType=VARCHAR},
      </if>
      <if test="targetDateRange != null">
        #{targetDateRange,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>

  <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.crm.model.entity.BDVisit.CityManagerVisitTargetUpdate">
    update crm_city_manager_visit_target
    <set>
      <if test="targetCreator != null">
        target_creator = #{targetCreator,jdbcType=INTEGER},
      </if>
      <if test="targetCreatorName != null">
        target_creator_name = #{targetCreatorName,jdbcType=VARCHAR},
      </if>
      <if test="bdNameList != null">
        bd_name_list = #{bdNameList,jdbcType=VARCHAR},
      </if>
      <if test="targetDateRange != null">
        target_date_range = #{targetDateRange,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="list" parameterType="net.summerfarm.crm.model.entity.BDVisit.CityManagerVisitTargetQuery" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from crm_city_manager_visit_target
    <where>
      <if test="id != null">
        and id = #{id,jdbcType=BIGINT}
      </if>
      <if test="targetCreator != null">
        and target_creator = #{targetCreator,jdbcType=INTEGER}
      </if>
      <if test="targetCreatorName != null and targetCreatorName != ''">
        and target_creator_name = #{targetCreatorName,jdbcType=VARCHAR}
      </if>
      <if test="bdNameList != null and bdNameList != ''">
        and bd_name_list = #{bdNameList,jdbcType=VARCHAR}
      </if>
      <if test="targetDateRange != null and targetDateRange != ''">
        and target_date_range = #{targetDateRange,jdbcType=VARCHAR}
      </if>
      <if test="createTime != null">
        and create_time = #{createTime,jdbcType=TIMESTAMP}
      </if>
      <if test="updateTime != null">
        and update_time = #{updateTime,jdbcType=TIMESTAMP}
      </if>
    </where>
    order by id desc
  </select>

</mapper>