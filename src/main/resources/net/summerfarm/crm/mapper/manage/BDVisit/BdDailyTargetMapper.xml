<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.crm.mapper.manage.BDVisit.BdVisitTargetMapper">
  
  <resultMap id="BaseResultMap" type="net.summerfarm.crm.model.domain.BDVisit.BdVisitTarget">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="city_manager_visit_target_id" jdbcType="BIGINT" property="cityManagerVisitTargetId" />
    <result column="bd_id" jdbcType="INTEGER" property="bdId" />
    <result column="bd_name" jdbcType="VARCHAR" property="bdName" />
    <result column="target_date" jdbcType="DATE" property="targetDate" />
    <result column="target_creator" jdbcType="INTEGER" property="targetCreator" />
    <result column="target_creator_name" jdbcType="VARCHAR" property="targetCreatorName" />
    <result column="visit_offline_count" jdbcType="INTEGER" property="visitOfflineCount" />
    <result column="visit_online_count" jdbcType="INTEGER" property="visitOnlineCount" />
    <result column="traffic_type" jdbcType="INTEGER" property="trafficType" />
  </resultMap>

  <sql id="Base_Column_List">
    id, create_time, update_time, city_manager_visit_target_id, bd_id, bd_name, target_date, target_creator, target_creator_name, visit_offline_count, visit_online_count, traffic_type
  </sql>

  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from crm_bd_visit_target
    where id = #{id,jdbcType=BIGINT}
  </select>

  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from crm_bd_visit_target
    where id = #{id,jdbcType=BIGINT}
  </delete>

  <insert id="batchInsert" parameterType="java.util.List" useGeneratedKeys="true" keyProperty="id">
    insert into crm_bd_visit_target (city_manager_visit_target_id, bd_id, bd_name, target_date, target_creator, target_creator_name, visit_offline_count, visit_online_count, traffic_type)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.cityManagerVisitTargetId,jdbcType=BIGINT}, #{item.bdId,jdbcType=INTEGER}, #{item.bdName,jdbcType=VARCHAR},
      #{item.targetDate,jdbcType=DATE}, #{item.targetCreator,jdbcType=INTEGER}, #{item.targetCreatorName,jdbcType=VARCHAR},
      #{item.visitOfflineCount,jdbcType=INTEGER}, #{item.visitOnlineCount,jdbcType=INTEGER}, #{item.trafficType,jdbcType=INTEGER})
    </foreach>
  </insert>

  <insert id="insertSelective" parameterType="net.summerfarm.crm.model.domain.BDVisit.BdVisitTarget" useGeneratedKeys="true" keyProperty="id">
    insert into crm_bd_visit_target
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="cityManagerVisitTargetId != null">
        city_manager_visit_target_id,
      </if>
      <if test="bdId != null">
        bd_id,
      </if>
      <if test="bdName != null">
        bd_name,
      </if>
      <if test="targetDate != null">
        target_date,
      </if>
      <if test="targetCreator != null">
        target_creator,
      </if>
      <if test="targetCreatorName != null">
        target_creator_name,
      </if>
      <if test="visitOfflineCount != null">
        visit_offline_count,
      </if>
      <if test="visitOnlineCount != null">
        visit_online_count,
      </if>
      <if test="trafficType != null">
        traffic_type,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="cityManagerVisitTargetId != null">
        #{cityManagerVisitTargetId,jdbcType=BIGINT},
      </if>
      <if test="bdId != null">
        #{bdId,jdbcType=INTEGER},
      </if>
      <if test="bdName != null">
        #{bdName,jdbcType=VARCHAR},
      </if>
      <if test="targetDate != null">
        #{targetDate,jdbcType=DATE},
      </if>
      <if test="targetCreator != null">
        #{targetCreator,jdbcType=INTEGER},
      </if>
      <if test="targetCreatorName != null">
        #{targetCreatorName,jdbcType=VARCHAR},
      </if>
      <if test="visitOfflineCount != null">
        #{visitOfflineCount,jdbcType=INTEGER},
      </if>
      <if test="visitOnlineCount != null">
        #{visitOnlineCount,jdbcType=INTEGER},
      </if>
      <if test="trafficType != null">
        #{trafficType,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>

  <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.crm.model.entity.BDVisit.BdVisitTargetUpdate">
    update crm_bd_visit_target
    <set>
      <if test="cityManagerVisitTargetId != null">
        city_manager_visit_target_id = #{cityManagerVisitTargetId,jdbcType=BIGINT},
      </if>
      <if test="bdId != null">
        bd_id = #{bdId,jdbcType=INTEGER},
      </if>
      <if test="bdName != null">
        bd_name = #{bdName,jdbcType=VARCHAR},
      </if>
      <if test="targetDate != null">
        target_date = #{targetDate,jdbcType=DATE},
      </if>
      <if test="targetCreator != null">
        target_creator = #{targetCreator,jdbcType=INTEGER},
      </if>
      <if test="targetCreatorName != null">
        target_creator_name = #{targetCreatorName,jdbcType=VARCHAR},
      </if>
      <if test="visitOfflineCount != null">
        visit_offline_count = #{visitOfflineCount,jdbcType=INTEGER},
      </if>
      <if test="visitOnlineCount != null">
        visit_online_count = #{visitOnlineCount,jdbcType=INTEGER},
      </if>
      <if test="trafficType != null">
        traffic_type = #{trafficType,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>

  <!-- 根据查询条件查询记录列表 -->
  <select id="list" parameterType="net.summerfarm.crm.model.entity.BDVisit.BdVisitTargetQuery" resultMap="BaseResultMap">
    SELECT
    <include refid="Base_Column_List" />
    FROM crm_bd_visit_target
    <where>
      <if test="id != null">
        AND id = #{id,jdbcType=BIGINT}
      </if>
      <if test="cityManagerVisitTargetId != null">
        AND city_manager_visit_target_id = #{cityManagerVisitTargetId,jdbcType=BIGINT}
      </if>
      <if test="bdId != null">
        AND bd_id = #{bdId,jdbcType=INTEGER}
      </if>
      <if test="bdName != null and bdName != ''">
        AND bd_name = #{bdName,jdbcType=VARCHAR}
      </if>
      <if test="targetDate != null">
        AND target_date = #{targetDate,jdbcType=DATE}
      </if>
      <if test="targetCreator != null">
        AND target_creator = #{targetCreator,jdbcType=INTEGER}
      </if>
      <if test="targetCreatorName != null and targetCreatorName != ''">
        AND target_creator_name = #{targetCreatorName,jdbcType=VARCHAR}
      </if>
      <if test="createTime != null">
        AND create_time = #{createTime,jdbcType=TIMESTAMP}
      </if>
      <if test="updateTime != null">
        AND update_time = #{updateTime,jdbcType=TIMESTAMP}
      </if>
    </where>
    ORDER BY id DESC
  </select>

  <!-- 根据销售ID列表和日期范围查询销售拜访目标 -->
  <select id="selectByBdIdsAndDateRange" resultMap="BaseResultMap">
    SELECT
    <include refid="Base_Column_List" />
    FROM crm_bd_visit_target
    WHERE bd_id IN
    <foreach collection="bdIds" item="bdId" open="(" close=")" separator=",">
      #{bdId,jdbcType=INTEGER}
    </foreach>
    AND target_date >= #{startDate,jdbcType=DATE}
    AND target_date <![CDATA[ <= ]]> #{endDate,jdbcType=DATE}
  </select>

</mapper>