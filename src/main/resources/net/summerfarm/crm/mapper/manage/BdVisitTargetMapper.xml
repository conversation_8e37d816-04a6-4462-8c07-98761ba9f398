<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.crm.mapper.manage.BDVisit.BdVisitTargetMapper">
  
  <resultMap id="BaseResultMap" type="net.summerfarm.crm.model.domain.BDVisit.BdVisitTarget">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="manager_visit_target_id" jdbcType="BIGINT" property="managerVisitTargetId" />
    <result column="sales_id" jdbcType="BIGINT" property="salesId" />
    <result column="target_date" jdbcType="DATE" property="targetDate" />
    <result column="target_value" jdbcType="DECIMAL" property="targetValue" />
    <result column="target_unit" jdbcType="VARCHAR" property="targetUnit" />
    <result column="target_description" jdbcType="VARCHAR" property="targetDescription" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>

  <sql id="Base_Column_List">
    id, manager_visit_target_id, sales_id, target_date, target_value, target_unit, target_description, creator, create_time, update_time
  </sql>

  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from crm_bd_visit_target
    where id = #{id,jdbcType=BIGINT}
  </select>

  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from crm_bd_visit_target
    where id = #{id,jdbcType=BIGINT}
  </delete>

  <insert id="insertBatch" parameterType="java.util.List" useGeneratedKeys="true" keyProperty="id">
    insert into crm_bd_visit_target (manager_visit_target_id, sales_id, target_date,
    target_value, target_unit, target_description, creator)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.managerVisitTargetId,jdbcType=BIGINT}, #{item.salesId,jdbcType=BIGINT}, #{item.targetDate,jdbcType=DATE},
      #{item.targetValue,jdbcType=DECIMAL}, #{item.targetUnit,jdbcType=VARCHAR}, #{item.targetDescription,jdbcType=VARCHAR}, #{item.creator,jdbcType=VARCHAR})
    </foreach>
  </insert>

  <insert id="insertSelective" parameterType="net.summerfarm.crm.model.domain.BDVisit.BdVisitTarget" useGeneratedKeys="true" keyProperty="id">
    insert into crm_bd_visit_target
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="managerVisitTargetId != null">
        manager_visit_target_id,
      </if>
      <if test="salesId != null">
        sales_id,
      </if>
      <if test="targetDate != null">
        target_date,
      </if>
      <if test="targetValue != null">
        target_value,
      </if>
      <if test="targetUnit != null">
        target_unit,
      </if>
      <if test="targetDescription != null">
        target_description,
      </if>
      <if test="creator != null">
        creator,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="managerVisitTargetId != null">
        #{managerVisitTargetId,jdbcType=BIGINT},
      </if>
      <if test="salesId != null">
        #{salesId,jdbcType=BIGINT},
      </if>
      <if test="targetDate != null">
        #{targetDate,jdbcType=DATE},
      </if>
      <if test="targetValue != null">
        #{targetValue,jdbcType=DECIMAL},
      </if>
      <if test="targetUnit != null">
        #{targetUnit,jdbcType=VARCHAR},
      </if>
      <if test="targetDescription != null">
        #{targetDescription,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>

  <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.crm.model.entity.BDVisit.BdVisitTargetUpdate">
    update crm_bd_visit_target
    <set>
      <if test="managerVisitTargetId != null">
        manager_visit_target_id = #{managerVisitTargetId,jdbcType=BIGINT},
      </if>
      <if test="salesId != null">
        sales_id = #{salesId,jdbcType=BIGINT},
      </if>
      <if test="targetDate != null">
        target_date = #{targetDate,jdbcType=DATE},
      </if>
      <if test="targetValue != null">
        target_value = #{targetValue,jdbcType=DECIMAL},
      </if>
      <if test="targetUnit != null">
        target_unit = #{targetUnit,jdbcType=VARCHAR},
      </if>
      <if test="targetDescription != null">
        target_description = #{targetDescription,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>

  <!-- 根据查询条件查询记录列表 -->
  <select id="list" parameterType="net.summerfarm.crm.model.entity.BDVisit.BdVisitTargetQuery" resultMap="BaseResultMap">
    SELECT
    <include refid="Base_Column_List" />
    FROM crm_bd_visit_target
    <where>
      <if test="id != null">
        AND id = #{id,jdbcType=BIGINT}
      </if>
      <if test="managerVisitTargetId != null">
        AND manager_visit_target_id = #{managerVisitTargetId,jdbcType=BIGINT}
      </if>
      <if test="salesId != null">
        AND sales_id = #{salesId,jdbcType=BIGINT}
      </if>
      <if test="targetDate != null">
        AND target_date = #{targetDate,jdbcType=DATE}
      </if>
      <if test="targetValue != null">
        AND target_value = #{targetValue,jdbcType=DECIMAL}
      </if>
      <if test="targetUnit != null and targetUnit != ''">
        AND target_unit = #{targetUnit,jdbcType=VARCHAR}
      </if>
      <if test="targetDescription != null and targetDescription != ''">
        AND target_description = #{targetDescription,jdbcType=VARCHAR}
      </if>
      <if test="creator != null and creator != ''">
        AND creator = #{creator,jdbcType=VARCHAR}
      </if>
      <if test="createTime != null">
        AND create_time = #{createTime,jdbcType=TIMESTAMP}
      </if>
      <if test="updateTime != null">
        AND update_time = #{updateTime,jdbcType=TIMESTAMP}
      </if>
    </where>
    ORDER BY id DESC
  </select>

</mapper>