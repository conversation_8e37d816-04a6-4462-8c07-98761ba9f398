<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.crm.mapper.manage.BDVisit.BdVisitPlanMapper">
  
  <resultMap id="BaseResultMap" type="net.summerfarm.crm.model.domain.BDVisit.BdVisitPlan">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="bd_daily_target_id" jdbcType="BIGINT" property="bdDailyTargetId" />
    <result column="bd_id" jdbcType="INTEGER" property="bdId" />
    <result column="bd_name" jdbcType="VARCHAR" property="bdName" />
    <result column="m_id" jdbcType="BIGINT" property="mId" />
    <result column="mname" jdbcType="VARCHAR" property="mname" />
    <result column="visit_order" jdbcType="INTEGER" property="visitOrder" />
    <result column="contact_id" jdbcType="BIGINT" property="contactId" />
    <result column="province" jdbcType="VARCHAR" property="province" />
    <result column="city" jdbcType="VARCHAR" property="city" />
    <result column="area" jdbcType="VARCHAR" property="area" />
    <result column="poi" jdbcType="VARCHAR" property="poi" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="lock_status" jdbcType="INTEGER" property="lockStatus" />
    <result column="visit_date" jdbcType="DATE" property="visitDate" />
    <result column="visit_type" jdbcType="INTEGER" property="visitType" />
    <result column="visit_status" jdbcType="INTEGER" property="visitStatus" />
    <result column="follow_up_record_id" jdbcType="INTEGER" property="followUpRecordId" />
    <result column="merchant_order_status" jdbcType="INTEGER" property="merchantOrderStatus" />
    <result column="merchant_potential_value" jdbcType="DECIMAL" property="merchantPotentialValue" />
  </resultMap>

  <sql id="Base_Column_List">
    id, create_time, update_time, bd_daily_target_id, bd_id, bd_name, m_id, mname, visit_order, contact_id, province, city, area, poi, status, lock_status, visit_date, visit_type, visit_status, follow_up_record_id, merchant_order_status, merchant_potential_value
  </sql>

  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from crm_bd_visit_plan
    where id = #{id,jdbcType=BIGINT}
  </select>

  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from crm_bd_visit_plan
    where id = #{id,jdbcType=BIGINT}
  </delete>

  <insert id="insertBatch" parameterType="java.util.List" useGeneratedKeys="true" keyProperty="id">
    insert into crm_bd_visit_plan (bd_daily_target_id, bd_id, bd_name, m_id, mname, visit_order, contact_id, province, city, area, poi, status, lock_status, visit_date, visit_type, visit_status, follow_up_record_id, merchant_order_status, merchant_potential_value)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.bdDailyTargetId,jdbcType=BIGINT}, #{item.bdId,jdbcType=INTEGER}, #{item.bdName,jdbcType=VARCHAR}, #{item.mId,jdbcType=BIGINT}, #{item.mname,jdbcType=VARCHAR}, #{item.visitOrder,jdbcType=INTEGER}, #{item.contactId,jdbcType=BIGINT}, #{item.province,jdbcType=VARCHAR}, #{item.city,jdbcType=VARCHAR}, #{item.area,jdbcType=VARCHAR}, #{item.poi,jdbcType=VARCHAR}, #{item.status,jdbcType=INTEGER}, #{item.lockStatus,jdbcType=INTEGER}, #{item.visitDate,jdbcType=DATE}, #{item.visitType,jdbcType=INTEGER}, #{item.visitStatus,jdbcType=INTEGER}, #{item.followUpRecordId,jdbcType=INTEGER}, #{item.merchantOrderStatus,jdbcType=INTEGER}, #{item.merchantPotentialValue,jdbcType=DECIMAL})
    </foreach>
  </insert>

  <insert id="insertSelective" parameterType="net.summerfarm.crm.model.domain.BDVisit.BdVisitPlan" useGeneratedKeys="true" keyProperty="id">
    insert into crm_bd_visit_plan
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="bdDailyTargetId != null">
        bd_daily_target_id,
      </if>
      <if test="bdId != null">
        bd_id,
      </if>
      <if test="bdName != null">
        bd_name,
      </if>
      <if test="mId != null">
        m_id,
      </if>
      <if test="mname != null">
        mname,
      </if>
      <if test="visitOrder != null">
        visit_order,
      </if>
      <if test="contactId != null">
        contact_id,
      </if>
      <if test="province != null">
        province,
      </if>
      <if test="city != null">
        city,
      </if>
      <if test="area != null">
        area,
      </if>
      <if test="poi != null">
        poi,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="lockStatus != null">
        lock_status,
      </if>
      <if test="visitDate != null">
        visit_date,
      </if>
      <if test="visitType != null">
        visit_type,
      </if>
      <if test="visitStatus != null">
        visit_status,
      </if>
      <if test="followUpRecordId != null">
        follow_up_record_id,
      </if>
      <if test="merchantOrderStatus != null">
        merchant_order_status,
      </if>
      <if test="merchantPotentialValue != null">
        merchant_potential_value,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="bdVisitTargetId != null">
        #{bdVisitTargetId,jdbcType=BIGINT},
      </if>
      <if test="bdId != null">
        #{bdId,jdbcType=INTEGER},
      </if>
      <if test="bdName != null">
        #{bdName,jdbcType=VARCHAR},
      </if>
      <if test="mId != null">
        #{mId,jdbcType=BIGINT},
      </if>
      <if test="mname != null">
        #{mname,jdbcType=VARCHAR},
      </if>
      <if test="visitOrder != null">
        #{visitOrder,jdbcType=INTEGER},
      </if>
      <if test="contactId != null">
        #{contactId,jdbcType=BIGINT},
      </if>
      <if test="province != null">
        #{province,jdbcType=VARCHAR},
      </if>
      <if test="city != null">
        #{city,jdbcType=VARCHAR},
      </if>
      <if test="area != null">
        #{area,jdbcType=VARCHAR},
      </if>
      <if test="poi != null">
        #{poi,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="lockStatus != null">
        #{lockStatus,jdbcType=INTEGER},
      </if>
      <if test="visitDate != null">
        #{visitDate,jdbcType=DATE},
      </if>
      <if test="visitType != null">
        #{visitType,jdbcType=INTEGER},
      </if>
      <if test="visitStatus != null">
        #{visitStatus,jdbcType=INTEGER},
      </if>
      <if test="followUpRecordId != null">
        #{followUpRecordId,jdbcType=INTEGER},
      </if>
      <if test="merchantOrderStatus != null">
        #{merchantOrderStatus,jdbcType=INTEGER},
      </if>
      <if test="merchantPotentialValue != null">
        #{merchantPotentialValue,jdbcType=DECIMAL},
      </if>
    </trim>
  </insert>

  <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.crm.model.entity.BDVisit.BdVisitPlanUpdate">
    update crm_bd_visit_plan
    <set>
      <if test="bdVisitTargetId != null">
        bd_visit_target_id = #{bdVisitTargetId,jdbcType=BIGINT},
      </if>
      <if test="bdId != null">
        bd_id = #{bdId,jdbcType=INTEGER},
      </if>
      <if test="bdName != null">
        bd_name = #{bdName,jdbcType=VARCHAR},
      </if>
      <if test="mId != null">
        m_id = #{mId,jdbcType=BIGINT},
      </if>
      <if test="mname != null">
        mname = #{mname,jdbcType=VARCHAR},
      </if>
      <if test="visitOrder != null">
        visit_order = #{visitOrder,jdbcType=INTEGER},
      </if>
      <if test="contactId != null">
        contact_id = #{contactId,jdbcType=BIGINT},
      </if>
      <if test="province != null">
        province = #{province,jdbcType=VARCHAR},
      </if>
      <if test="city != null">
        city = #{city,jdbcType=VARCHAR},
      </if>
      <if test="area != null">
        area = #{area,jdbcType=VARCHAR},
      </if>
      <if test="poi != null">
        poi = #{poi,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=INTEGER},
      </if>
      <if test="lockStatus != null">
        lock_status = #{lockStatus,jdbcType=INTEGER},
      </if>
      <if test="visitDate != null">
        visit_date = #{visitDate,jdbcType=DATE},
      </if>
      <if test="visitType != null">
        visit_type = #{visitType,jdbcType=INTEGER},
      </if>
      <if test="visitStatus != null">
        visit_status = #{visitStatus,jdbcType=INTEGER},
      </if>
      <if test="followUpRecordId != null">
        follow_up_record_id = #{followUpRecordId,jdbcType=INTEGER},
      </if>
      <if test="merchantOrderStatus != null">
        merchant_order_status = #{merchantOrderStatus,jdbcType=INTEGER},
      </if>
      <if test="merchantPotentialValue != null">
        merchant_potential_value = #{merchantPotentialValue,jdbcType=DECIMAL},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>

  <!-- 根据查询条件查询记录列表 -->
  <select id="list" parameterType="net.summerfarm.crm.model.entity.BDVisit.BdVisitPlanQuery" resultMap="BaseResultMap">
    SELECT
    <include refid="Base_Column_List" />
    FROM crm_bd_visit_plan
    <where>
      <if test="id != null">
        AND id = #{id,jdbcType=BIGINT}
      </if>
      <if test="bdVisitTargetId != null">
        AND bd_visit_target_id = #{bdVisitTargetId,jdbcType=BIGINT}
      </if>
      <if test="bdId != null">
        AND bd_id = #{bdId,jdbcType=INTEGER}
      </if>
      <if test="bdName != null and bdName != ''">
        AND bd_name = #{bdName,jdbcType=VARCHAR}
      </if>
      <if test="mId != null">
        AND m_id = #{mId,jdbcType=BIGINT}
      </if>
      <if test="mname != null and mname != ''">
        AND mname = #{mname,jdbcType=VARCHAR}
      </if>
      <if test="visitDate != null">
        AND visit_date = #{visitDate,jdbcType=DATE}
      </if>
      <if test="status != null">
        AND status = #{status,jdbcType=INTEGER}
      </if>
      <if test="visitStatus != null">
        AND visit_status = #{visitStatus,jdbcType=INTEGER}
      </if>
      <if test="createTime != null">
        AND create_time = #{createTime,jdbcType=TIMESTAMP}
      </if>
      <if test="updateTime != null">
        AND update_time = #{updateTime,jdbcType=TIMESTAMP}
      </if>
    </where>
    ORDER BY id DESC
  </select>

  <!-- 根据门店ID和拜访目标ID批量更新门店潜力值 -->
  <update id="batchUpdatePotentialValueByMerchantIdAndTargetId">
    <foreach collection="updateList" item="item" separator=";">
      UPDATE crm_bd_visit_plan
      SET merchant_potential_value = #{item.merchantPotentialValue,jdbcType=DECIMAL},
          update_time = NOW()
      WHERE bd_visit_target_id = #{item.bdVisitTargetId,jdbcType=BIGINT}
        AND m_id = #{item.mId,jdbcType=BIGINT}
    </foreach>
  </update>

</mapper>