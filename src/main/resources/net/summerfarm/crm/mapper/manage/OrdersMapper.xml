<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.crm.mapper.manage.OrdersMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.crm.model.domain.Orders">
    <id column="order_id" jdbcType="BIGINT" property="orderId" />
    <result column="order_no" jdbcType="VARCHAR" property="orderNo" />
    <result column="m_id" jdbcType="BIGINT" property="mId" />
    <result column="order_time" jdbcType="TIMESTAMP" property="orderTime" />
    <result column="type" jdbcType="INTEGER" property="type" />
    <result column="status" jdbcType="SMALLINT" property="status" />
    <result column="delivery_fee" jdbcType="DECIMAL" property="deliveryFee" />
    <result column="total_price" jdbcType="DECIMAL" property="totalPrice" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="confirm_time" jdbcType="TIMESTAMP" property="confirmTime" />
    <result column="area_name" jdbcType="VARCHAR" property="areaName" />
    <result column="out_times" jdbcType="INTEGER" property="outTimes" />
    <result column="discount_type" jdbcType="INTEGER" property="discountType" />
    <result column="out_times_fee" jdbcType="DECIMAL" property="outTimesFee" />
    <result column="area_no" jdbcType="INTEGER" property="areaNo" />
    <result column="m_size" jdbcType="VARCHAR" property="mSize" />
    <result column="direct" jdbcType="INTEGER" property="direct" />
    <result column="sku_show" jdbcType="INTEGER" property="skuShow" />
    <result column="red_pack_amount" jdbcType="DECIMAL" property="redPackAmount" />
    <result column="card_rule_id" jdbcType="INTEGER" property="cardRuleId" />
    <result column="account_id" jdbcType="BIGINT" property="accountId" />
    <result column="origin_price" jdbcType="DECIMAL" property="originPrice" />
    <result column="out_stock" jdbcType="INTEGER" property="outStock" />
    <result column="discount_card_id" jdbcType="INTEGER" property="discountCardId" />
    <result column="order_sale_type" jdbcType="INTEGER" property="orderSaleType" />
    <result column="receivable_status" jdbcType="SMALLINT" property="receivableStatus" />
    <result column="admin_id" jdbcType="INTEGER" property="adminId" />
    <result column="invoice_status" jdbcType="TINYINT" property="invoiceStatus" />
    <result column="financial_invoice_id" jdbcType="BIGINT" property="financialInvoiceId" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="operate_id" jdbcType="INTEGER" property="operateId" />
  </resultMap>
  <sql id="Base_Column_List">
    order_id, order_no, m_id, order_time, `type`, `status`, delivery_fee, total_price, 
    remark, confirm_time, area_name, out_times, discount_type, out_times_fee, area_no, 
    m_size, direct, sku_show, red_pack_amount, card_rule_id, account_id, origin_price, 
    out_stock, discount_card_id, order_sale_type, receivable_status, admin_id, invoice_status, 
    financial_invoice_id, update_time, operate_id
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from orders
    where order_id = #{orderId,jdbcType=BIGINT}
  </select>
  <select id="selectOrderDeliveryByMid" resultType="net.summerfarm.crm.model.vo.OrderDeliveryVO">
    select
    o.order_no as orderNo, o.order_time as orderTime,  o.m_id as mId, o.total_price as orderAmount, o.status as orderStatus, d.status as deliveryStatus,
    d.contact_id as contactId, d.id as deliveryPlanId
    from orders o left join delivery_plan d on o.order_no = d.order_no
    where o.status in (2, 3, 6)
    <if test="mIds != null and mIds.size > 0">
      and o.m_id in
      <foreach collection="mIds" open="(" close=")" item="item" separator=",">
        #{item}
      </foreach>
    </if>
    and d.delivery_time = #{deliveryTime} and deliverytype=0
    order by o.order_time desc
  </select>
    <select id="selectByOrderNo" resultType="net.summerfarm.crm.model.domain.Orders">
      SELECT m_id mId, account_id accountId, `type`, order_no orderNo
      FROM orders
      <where>
        <if test="orderNo != null">
          AND order_no = #{orderNo}
        </if>
      </where>
    </select>
  <select id="twoDaysAveragePrice" resultType="java.math.BigDecimal">
    select sum(oi.price) / count(amount)
    from order_item oi
           LEFT JOIN orders o on o.`order_no` = oi.`order_no`
    where oi.sku = #{sku} and o.area_no=#{areaNo} and o.status in (2 , 3, 6)
    and  `order_time` BETWEEN TIMESTAMP (date_sub(curdate(), interval + 2 day)) and date_sub( TIMESTAMP (curdate()) , INTERVAL +1 second)
  </select>
  <select id="countOrderBySpuAndMerchantDuringXDay" resultType="java.lang.Integer">
    SELECT count(*)
    from `orders` os
           LEFT JOIN `order_item` oi on os.`order_no` = oi.`order_no`
    where oi.sku in
    <foreach collection="skus" open="(" close=")" item="item" separator=",">
      #{item}
    </foreach>
     and os.`order_time` BETWEEN TIMESTAMP (date_sub(curdate(), interval + #{numOfDay} day))and now()
        and os.`m_id`=#{mId} and os.status in(2,3,6)
  </select>
  <select id="countSixtyDayOrderByCategoryAndMerchant" resultType="java.lang.Integer">
    select COUNT(*)
    from `order_item` oi
           INNER JOIN `orders` os on os.`order_no` = oi.`order_no`
    where os.`order_time` BETWEEN TIMESTAMP (date_sub(curdate(), interval + 60 day))
      and now() and `category_id` = #{categoryId} and os.`m_id` = #{mId} and os.status in(2,3,6)
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from orders
    where order_id = #{orderId,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="order_id" keyProperty="orderId" parameterType="net.summerfarm.crm.model.domain.Orders" useGeneratedKeys="true">
    insert into orders (order_no, m_id, order_time, 
      `type`, `status`, delivery_fee, 
      total_price, remark, confirm_time, 
      area_name, out_times, discount_type, 
      out_times_fee, area_no, m_size, 
      direct, sku_show, red_pack_amount, 
      card_rule_id, account_id, origin_price, 
      out_stock, discount_card_id, order_sale_type, 
      receivable_status, admin_id, invoice_status, 
      financial_invoice_id, update_time, operate_id
      )
    values (#{orderNo,jdbcType=VARCHAR}, #{mId,jdbcType=BIGINT}, #{orderTime,jdbcType=TIMESTAMP}, 
      #{type,jdbcType=INTEGER}, #{status,jdbcType=SMALLINT}, #{deliveryFee,jdbcType=DECIMAL}, 
      #{totalPrice,jdbcType=DECIMAL}, #{remark,jdbcType=VARCHAR}, #{confirmTime,jdbcType=TIMESTAMP}, 
      #{areaName,jdbcType=VARCHAR}, #{outTimes,jdbcType=INTEGER}, #{discountType,jdbcType=INTEGER}, 
      #{outTimesFee,jdbcType=DECIMAL}, #{areaNo,jdbcType=INTEGER}, #{mSize,jdbcType=VARCHAR}, 
      #{direct,jdbcType=INTEGER}, #{skuShow,jdbcType=INTEGER}, #{redPackAmount,jdbcType=DECIMAL}, 
      #{cardRuleId,jdbcType=INTEGER}, #{accountId,jdbcType=BIGINT}, #{originPrice,jdbcType=DECIMAL}, 
      #{outStock,jdbcType=INTEGER}, #{discountCardId,jdbcType=INTEGER}, #{orderSaleType,jdbcType=INTEGER}, 
      #{receivableStatus,jdbcType=SMALLINT}, #{adminId,jdbcType=INTEGER}, #{invoiceStatus,jdbcType=TINYINT}, 
      #{financialInvoiceId,jdbcType=BIGINT}, #{updateTime,jdbcType=TIMESTAMP}, #{operateId,jdbcType=INTEGER}
      )
  </insert>
  <insert id="insertSelective" keyColumn="order_id" keyProperty="orderId" parameterType="net.summerfarm.crm.model.domain.Orders" useGeneratedKeys="true">
    insert into orders
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="orderNo != null">
        order_no,
      </if>
      <if test="mId != null">
        m_id,
      </if>
      <if test="orderTime != null">
        order_time,
      </if>
      <if test="type != null">
        `type`,
      </if>
      <if test="status != null">
        `status`,
      </if>
      <if test="deliveryFee != null">
        delivery_fee,
      </if>
      <if test="totalPrice != null">
        total_price,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="confirmTime != null">
        confirm_time,
      </if>
      <if test="areaName != null">
        area_name,
      </if>
      <if test="outTimes != null">
        out_times,
      </if>
      <if test="discountType != null">
        discount_type,
      </if>
      <if test="outTimesFee != null">
        out_times_fee,
      </if>
      <if test="areaNo != null">
        area_no,
      </if>
      <if test="mSize != null">
        m_size,
      </if>
      <if test="direct != null">
        direct,
      </if>
      <if test="skuShow != null">
        sku_show,
      </if>
      <if test="redPackAmount != null">
        red_pack_amount,
      </if>
      <if test="cardRuleId != null">
        card_rule_id,
      </if>
      <if test="accountId != null">
        account_id,
      </if>
      <if test="originPrice != null">
        origin_price,
      </if>
      <if test="outStock != null">
        out_stock,
      </if>
      <if test="discountCardId != null">
        discount_card_id,
      </if>
      <if test="orderSaleType != null">
        order_sale_type,
      </if>
      <if test="receivableStatus != null">
        receivable_status,
      </if>
      <if test="adminId != null">
        admin_id,
      </if>
      <if test="invoiceStatus != null">
        invoice_status,
      </if>
      <if test="financialInvoiceId != null">
        financial_invoice_id,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="operateId != null">
        operate_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="orderNo != null">
        #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="mId != null">
        #{mId,jdbcType=BIGINT},
      </if>
      <if test="orderTime != null">
        #{orderTime,jdbcType=TIMESTAMP},
      </if>
      <if test="type != null">
        #{type,jdbcType=INTEGER},
      </if>
      <if test="status != null">
        #{status,jdbcType=SMALLINT},
      </if>
      <if test="deliveryFee != null">
        #{deliveryFee,jdbcType=DECIMAL},
      </if>
      <if test="totalPrice != null">
        #{totalPrice,jdbcType=DECIMAL},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="confirmTime != null">
        #{confirmTime,jdbcType=TIMESTAMP},
      </if>
      <if test="areaName != null">
        #{areaName,jdbcType=VARCHAR},
      </if>
      <if test="outTimes != null">
        #{outTimes,jdbcType=INTEGER},
      </if>
      <if test="discountType != null">
        #{discountType,jdbcType=INTEGER},
      </if>
      <if test="outTimesFee != null">
        #{outTimesFee,jdbcType=DECIMAL},
      </if>
      <if test="areaNo != null">
        #{areaNo,jdbcType=INTEGER},
      </if>
      <if test="mSize != null">
        #{mSize,jdbcType=VARCHAR},
      </if>
      <if test="direct != null">
        #{direct,jdbcType=INTEGER},
      </if>
      <if test="skuShow != null">
        #{skuShow,jdbcType=INTEGER},
      </if>
      <if test="redPackAmount != null">
        #{redPackAmount,jdbcType=DECIMAL},
      </if>
      <if test="cardRuleId != null">
        #{cardRuleId,jdbcType=INTEGER},
      </if>
      <if test="accountId != null">
        #{accountId,jdbcType=BIGINT},
      </if>
      <if test="originPrice != null">
        #{originPrice,jdbcType=DECIMAL},
      </if>
      <if test="outStock != null">
        #{outStock,jdbcType=INTEGER},
      </if>
      <if test="discountCardId != null">
        #{discountCardId,jdbcType=INTEGER},
      </if>
      <if test="orderSaleType != null">
        #{orderSaleType,jdbcType=INTEGER},
      </if>
      <if test="receivableStatus != null">
        #{receivableStatus,jdbcType=SMALLINT},
      </if>
      <if test="adminId != null">
        #{adminId,jdbcType=INTEGER},
      </if>
      <if test="invoiceStatus != null">
        #{invoiceStatus,jdbcType=TINYINT},
      </if>
      <if test="financialInvoiceId != null">
        #{financialInvoiceId,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="operateId != null">
        #{operateId,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.crm.model.domain.Orders">
    update orders
    <set>
      <if test="orderNo != null">
        order_no = #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="mId != null">
        m_id = #{mId,jdbcType=BIGINT},
      </if>
      <if test="orderTime != null">
        order_time = #{orderTime,jdbcType=TIMESTAMP},
      </if>
      <if test="type != null">
        `type` = #{type,jdbcType=INTEGER},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=SMALLINT},
      </if>
      <if test="deliveryFee != null">
        delivery_fee = #{deliveryFee,jdbcType=DECIMAL},
      </if>
      <if test="totalPrice != null">
        total_price = #{totalPrice,jdbcType=DECIMAL},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="confirmTime != null">
        confirm_time = #{confirmTime,jdbcType=TIMESTAMP},
      </if>
      <if test="areaName != null">
        area_name = #{areaName,jdbcType=VARCHAR},
      </if>
      <if test="outTimes != null">
        out_times = #{outTimes,jdbcType=INTEGER},
      </if>
      <if test="discountType != null">
        discount_type = #{discountType,jdbcType=INTEGER},
      </if>
      <if test="outTimesFee != null">
        out_times_fee = #{outTimesFee,jdbcType=DECIMAL},
      </if>
      <if test="areaNo != null">
        area_no = #{areaNo,jdbcType=INTEGER},
      </if>
      <if test="mSize != null">
        m_size = #{mSize,jdbcType=VARCHAR},
      </if>
      <if test="direct != null">
        direct = #{direct,jdbcType=INTEGER},
      </if>
      <if test="skuShow != null">
        sku_show = #{skuShow,jdbcType=INTEGER},
      </if>
      <if test="redPackAmount != null">
        red_pack_amount = #{redPackAmount,jdbcType=DECIMAL},
      </if>
      <if test="cardRuleId != null">
        card_rule_id = #{cardRuleId,jdbcType=INTEGER},
      </if>
      <if test="accountId != null">
        account_id = #{accountId,jdbcType=BIGINT},
      </if>
      <if test="originPrice != null">
        origin_price = #{originPrice,jdbcType=DECIMAL},
      </if>
      <if test="outStock != null">
        out_stock = #{outStock,jdbcType=INTEGER},
      </if>
      <if test="discountCardId != null">
        discount_card_id = #{discountCardId,jdbcType=INTEGER},
      </if>
      <if test="orderSaleType != null">
        order_sale_type = #{orderSaleType,jdbcType=INTEGER},
      </if>
      <if test="receivableStatus != null">
        receivable_status = #{receivableStatus,jdbcType=SMALLINT},
      </if>
      <if test="adminId != null">
        admin_id = #{adminId,jdbcType=INTEGER},
      </if>
      <if test="invoiceStatus != null">
        invoice_status = #{invoiceStatus,jdbcType=TINYINT},
      </if>
      <if test="financialInvoiceId != null">
        financial_invoice_id = #{financialInvoiceId,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="operateId != null">
        operate_id = #{operateId,jdbcType=INTEGER},
      </if>
    </set>
    where order_id = #{orderId,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="net.summerfarm.crm.model.domain.Orders">
    update orders
    set order_no = #{orderNo,jdbcType=VARCHAR},
      m_id = #{mId,jdbcType=BIGINT},
      order_time = #{orderTime,jdbcType=TIMESTAMP},
      `type` = #{type,jdbcType=INTEGER},
      `status` = #{status,jdbcType=SMALLINT},
      delivery_fee = #{deliveryFee,jdbcType=DECIMAL},
      total_price = #{totalPrice,jdbcType=DECIMAL},
      remark = #{remark,jdbcType=VARCHAR},
      confirm_time = #{confirmTime,jdbcType=TIMESTAMP},
      area_name = #{areaName,jdbcType=VARCHAR},
      out_times = #{outTimes,jdbcType=INTEGER},
      discount_type = #{discountType,jdbcType=INTEGER},
      out_times_fee = #{outTimesFee,jdbcType=DECIMAL},
      area_no = #{areaNo,jdbcType=INTEGER},
      m_size = #{mSize,jdbcType=VARCHAR},
      direct = #{direct,jdbcType=INTEGER},
      sku_show = #{skuShow,jdbcType=INTEGER},
      red_pack_amount = #{redPackAmount,jdbcType=DECIMAL},
      card_rule_id = #{cardRuleId,jdbcType=INTEGER},
      account_id = #{accountId,jdbcType=BIGINT},
      origin_price = #{originPrice,jdbcType=DECIMAL},
      out_stock = #{outStock,jdbcType=INTEGER},
      discount_card_id = #{discountCardId,jdbcType=INTEGER},
      order_sale_type = #{orderSaleType,jdbcType=INTEGER},
      receivable_status = #{receivableStatus,jdbcType=SMALLINT},
      admin_id = #{adminId,jdbcType=INTEGER},
      invoice_status = #{invoiceStatus,jdbcType=TINYINT},
      financial_invoice_id = #{financialInvoiceId,jdbcType=BIGINT},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      operate_id = #{operateId,jdbcType=INTEGER}
    where order_id = #{orderId,jdbcType=BIGINT}
  </update>

  <select id="getUnfilledOrderByMid" resultType="java.lang.Integer" parameterType="java.lang.Long">
      SELECT count(*)
      FROM orders
      WHERE m_id = #{mId}
        and status in (1, 2, 3)
    </select>


  <select id="getAfterUnfilledOrderByMid" resultType="java.lang.Integer" parameterType="java.lang.Long">
      SELECT count(*)
      FROM after_sale_order
      WHERE m_id = #{mId}
        and status in (0,1,4,12)
    </select>

  <select id="listTimingRemindOrder" resultType="net.summerfarm.crm.model.query.task.TimingRemindOrder">
    SELECT o.`order_no` orderNo,
           fur.admin_id bdId,
           m.mname,
           m.m_id       mId
    FROM orders o
           LEFT JOIN delivery_plan dp ON o.order_no = dp.order_no
           join `follow_up_relation` fur on o.`m_id` = fur.m_id and reassign = 0
           join `merchant` m on o.`m_id` = m.m_id
    WHERE o.type = 1
      and o.order_time BETWEEN DATE_SUB(DATE_FORMAT(CURDATE(), '%Y-%m-%d %H:%i:%s'), INTERVAL 2 DAY) AND
      DATE_ADD(DATE(DATE_SUB(NOW(), INTERVAL 2 DAY)), INTERVAL '23:59:59' HOUR_SECOND)
      AND dp.delivery_time IS NULL
    </select>

  <select id="getOverOrder" resultType="net.summerfarm.crm.model.domain.Orders">
  SELECT
  <include refid="Base_Column_List" />
  FROM orders
  WHERE m_id = #{mId}
  and status in (2,3,6)
  limit 1
  </select>

  <select id="selectSixtyDaySpuByMerchant" resultType="java.lang.String">
    SELECT distinct(oi.pd_name)
    from `orders` os
    LEFT JOIN `order_item` oi on os.`order_no` = oi.`order_no`
    where os.`order_time` BETWEEN TIMESTAMP (date_sub(curdate(), interval + 60 day))and now()
    and os.`m_id`=#{mId} and os.status in(2,3,6)
    order by os.order_time desc
  </select>

  <select id="selectSixtyDaySpuWithCategoryTypeByMerchant" resultType="net.summerfarm.crm.model.dto.PdNameCategoryTypeDTO">
    SELECT distinct(oi.pd_name) as pdName, c.type as type
    from `orders` os
    LEFT JOIN `order_item` oi on os.`order_no` = oi.`order_no`
    left join category c on oi.category_id = c.id
    where os.`order_time` BETWEEN TIMESTAMP (date_sub(curdate(), interval + 60 day))and now()
    and os.`m_id`=#{mId} and os.status in(2,3,6)
    order by os.order_time desc
  </select>

  <select id="selectXDayPdIdByMId" resultType="net.summerfarm.crm.model.dto.MerchantRecentSpuDTO">
    SELECT DISTINCT i.pd_id as pdId, oi.pd_name as pdName, c.type as type
    from `orders` os
    LEFT JOIN `order_item` oi on os.`order_no` = oi.`order_no`
    left join inventory i on i.sku = oi.sku
    left join category c on oi.category_id = c.id
    where os.`order_time` BETWEEN TIMESTAMP (date_sub(curdate(), interval #{numOfDay} day))and now()
    and os.`m_id`=#{mId} and os.status in(2,3,6)
    and i.pd_id is not null
    order by os.order_time desc
  </select>

  <select id="selectShortTermLostSpuByMerchant" resultType="net.summerfarm.crm.model.dto.ShortTermLostSpuDTO">
    /*FORCE_SLAVE*/
    SELECT oi.pd_name as pdName
    , i.pd_id AS pdId
    , MAX(o.order_time) AS lastOrderDate
    FROM orders o
    inner JOIN order_item oi
    ON o.order_no = oi.order_no
    inner JOIN inventory i
    ON oi.sku = i.sku
    WHERE o.m_id = #{mId}
    AND o.status IN (2, 3, 6)
    AND o.order_time  <![CDATA[>=]]> DATE_SUB(NOW(), INTERVAL #{months} MONTH) -- 近6个月下过单
    GROUP BY i.pd_id
    HAVING lastOrderDate  <![CDATA[<]]> DATE_SUB(NOW(), INTERVAL #{lostDays} DAY) -- 近15天未下单;
  </select>

  <select id="getLatestOrderBySku" resultType="net.summerfarm.crm.model.dto.merchantsituation.approval.RecentOrderDTO">
    SELECT
    oi.sku AS sku,
    o.order_no AS orderNo,
    o.type AS type,
    o.order_time AS orderTime,
    oi.price AS price,
    oi.amount AS amount
    FROM orders o
    left join order_item oi on o.order_no = oi.order_no
    WHERE oi.sku = #{sku}
    AND o.m_id = #{mId}
    AND oi.status in (2,3,6)
    ORDER BY o.order_time DESC
    LIMIT 1
  </select>

  <select id="getLatestFulfilledOrderBySku"
          resultType="net.summerfarm.crm.model.dto.merchantsituation.approval.RecentOrderDTO">
    SELECT
    oi.sku AS sku,
    o.order_no AS orderNo,
    o.type AS type,
    o.order_time AS orderTime,
    oi.price AS price,
    oi.amount AS amount
    FROM orders o
    left join order_item oi on o.order_no = oi.order_no
    left join delivery_plan dp on o.order_no = dp.order_no
    WHERE oi.sku = #{sku}
    AND o.m_id = #{mId}
    AND oi.status in (2,3,6)
    AND dp.status = 6
    ORDER BY dp.delivery_time DESC
    LIMIT 1
  </select>

  <select id="getLastOrderInfo" resultType="net.summerfarm.crm.model.dto.followUpRelation.LastOrderDTO">
    SELECT COUNT(DISTINCT `order_no`) AS orderCount, MAX(`order_time`) AS lastOrderTime FROM `orders`
    WHERE `m_id` = #{mId} AND status in
    <foreach open="(" close=")" separator="," collection="statusList" item="item" index="index">
      #{item}
    </foreach>
  </select>

  <select id="listLastOrderInfo" resultType="net.summerfarm.crm.model.dto.followUpRelation.LastOrderDTO">
    SELECT `m_id` AS mId, COUNT(DISTINCT `order_no`) AS orderCount, MAX(`order_time`) AS lastOrderTime
    FROM `orders`
    WHERE `m_id` IN
    <foreach open="(" close=")" separator="," collection="mIds" item="mId" index="index">
      #{mId}
    </foreach>
    AND `status` IN
    <foreach open="(" close=")" separator="," collection="statusList" item="item" index="index">
      #{item}
    </foreach>
    GROUP BY `m_id`
  </select>

  <select id="getMidAndAdminIdBetweenOrderTime" resultType="net.summerfarm.crm.model.domain.Orders">
    SELECT m_id AS mId, `admin_id` AS adminId FROM `orders`
    WHERE order_time >= #{orderTimeBegin}
    AND `order_time` <![CDATA[ < ]]> #{orderTimeEnd}
    AND `status` IN
    <foreach open="(" close=")" separator="," collection="statusList" item="item" index="index">
      #{item}
    </foreach>
    ORDER BY `order_time` DESC
  </select>

</mapper>