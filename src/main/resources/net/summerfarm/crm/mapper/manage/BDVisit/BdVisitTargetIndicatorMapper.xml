<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.crm.mapper.manage.BDVisit.BdVisitTargetIndicatorMapper">
  
  <resultMap id="BaseResultMap" type="net.summerfarm.crm.model.domain.BDVisit.BdVisitTargetIndicator">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="city_manager_visit_target_id" jdbcType="BIGINT" property="cityManagerVisitTargetId" />
    <result column="bd_visit_target_id" jdbcType="BIGINT" property="bdVisitTargetId" />
    <result column="bd_id" jdbcType="INTEGER" property="bdId" />
    <result column="bd_name" jdbcType="VARCHAR" property="bdName" />
    <result column="priority" jdbcType="INTEGER" property="priority" />
    <result column="indicator_type" jdbcType="INTEGER" property="indicatorType" />
    <result column="indicator_name" jdbcType="VARCHAR" property="indicatorName" />
    <result column="target_date" jdbcType="DATE" property="targetDate" />
    <result column="business_type" jdbcType="INTEGER" property="businessType" />
    <result column="metrics_method" jdbcType="INTEGER" property="metricsMethod" />
    <result column="category_name" jdbcType="VARCHAR" property="categoryName" />
    <result column="sku" jdbcType="VARCHAR" property="sku" />
    <result column="indicator_expected_value" jdbcType="DECIMAL" property="indicatorExpectedValue" />
    <result column="indicator_status" jdbcType="INTEGER" property="indicatorStatus" />
    <result column="indicator_current_value" jdbcType="DECIMAL" property="indicatorCurrentValue" />
  </resultMap>

  <sql id="Base_Column_List">
    id, create_time, update_time, city_manager_visit_target_id, bd_visit_target_id, bd_id, bd_name, priority, indicator_type, indicator_name, target_date, business_type, metrics_method, category_name, sku, indicator_expected_value, indicator_status, indicator_current_value
  </sql>

  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from crm_bd_visit_target_indicator
    where id = #{id,jdbcType=BIGINT}
  </select>

  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from crm_bd_visit_target_indicator
    where id = #{id,jdbcType=BIGINT}
  </delete>

  <insert id="batchInsert" parameterType="java.util.List" useGeneratedKeys="true" keyProperty="id">
    insert into crm_bd_visit_target_indicator (city_manager_visit_target_id, bd_visit_target_id, bd_id, bd_name, priority,
    indicator_type, indicator_name, target_date, business_type, metrics_method, category_name, sku, indicator_expected_value, indicator_status, indicator_current_value)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.cityManagerVisitTargetId,jdbcType=BIGINT}, #{item.bdVisitTargetId,jdbcType=BIGINT}, #{item.bdId,jdbcType=INTEGER}, #{item.bdName,jdbcType=VARCHAR}, #{item.priority,jdbcType=INTEGER},
      #{item.indicatorType,jdbcType=INTEGER}, #{item.indicatorName,jdbcType=VARCHAR}, #{item.targetDate,jdbcType=DATE}, #{item.businessType,jdbcType=INTEGER}, #{item.metricsMethod,jdbcType=INTEGER}, #{item.categoryName,jdbcType=VARCHAR}, #{item.sku,jdbcType=VARCHAR}, #{item.indicatorExpectedValue,jdbcType=DECIMAL}, #{item.indicatorStatus,jdbcType=INTEGER}, #{item.indicatorCurrentValue,jdbcType=DECIMAL})
    </foreach>
  </insert>

  <insert id="insertSelective" parameterType="net.summerfarm.crm.model.domain.BDVisit.BdVisitTargetIndicator" useGeneratedKeys="true" keyProperty="id">
    insert into crm_bd_visit_target_indicator
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="cityManagerVisitTargetId != null">
        city_manager_visit_target_id,
      </if>
      <if test="bdVisitTargetId != null">
        bd_visit_target_id,
      </if>
      <if test="bdId != null">
        bd_id,
      </if>
      <if test="bdName != null">
        bd_name,
      </if>
      <if test="priority != null">
        priority,
      </if>
      <if test="indicatorType != null">
        indicator_type,
      </if>
      <if test="indicatorName != null">
        indicator_name,
      </if>
      <if test="targetDate != null">
        target_date,
      </if>
      <if test="businessType != null">
        business_type,
      </if>
      <if test="metricsMethod != null">
        metrics_method,
      </if>
      <if test="categoryName != null">
        category_name,
      </if>
      <if test="sku != null">
        sku,
      </if>
      <if test="indicatorExpectedValue != null">
        indicator_expected_value,
      </if>
      <if test="indicatorStatus != null">
        indicator_status,
      </if>
      <if test="indicatorCurrentValue != null">
        indicator_current_value,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="cityManagerVisitTargetId != null">
        #{cityManagerVisitTargetId,jdbcType=BIGINT},
      </if>
      <if test="bdVisitTargetId != null">
        #{bdVisitTargetId,jdbcType=BIGINT},
      </if>
      <if test="bdId != null">
        #{bdId,jdbcType=INTEGER},
      </if>
      <if test="bdName != null">
        #{bdName,jdbcType=VARCHAR},
      </if>
      <if test="priority != null">
        #{priority,jdbcType=INTEGER},
      </if>
      <if test="indicatorType != null">
        #{indicatorType,jdbcType=INTEGER},
      </if>
      <if test="indicatorName != null">
        #{indicatorName,jdbcType=VARCHAR},
      </if>
      <if test="targetDate != null">
        #{targetDate,jdbcType=DATE},
      </if>
      <if test="businessType != null">
        #{businessType,jdbcType=INTEGER},
      </if>
      <if test="metricsMethod != null">
        #{metricsMethod,jdbcType=INTEGER},
      </if>
      <if test="categoryName != null">
        #{categoryName,jdbcType=VARCHAR},
      </if>
      <if test="sku != null">
        #{sku,jdbcType=VARCHAR},
      </if>
      <if test="indicatorExpectedValue != null">
        #{indicatorExpectedValue,jdbcType=DECIMAL},
      </if>
      <if test="indicatorStatus != null">
        #{indicatorStatus,jdbcType=INTEGER},
      </if>
      <if test="indicatorCurrentValue != null">
        #{indicatorCurrentValue,jdbcType=DECIMAL},
      </if>
    </trim>
  </insert>

  <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.crm.model.entity.BDVisit.BdVisitTargetIndicatorUpdate">
    update crm_bd_visit_target_indicator
    <set>
      <if test="cityManagerVisitTargetId != null">
        city_manager_visit_target_id = #{cityManagerVisitTargetId,jdbcType=BIGINT},
      </if>
      <if test="bdVisitTargetId != null">
        bd_visit_target_id = #{bdVisitTargetId,jdbcType=BIGINT},
      </if>
      <if test="bdId != null">
        bd_id = #{bdId,jdbcType=INTEGER},
      </if>
      <if test="bdName != null">
        bd_name = #{bdName,jdbcType=VARCHAR},
      </if>
      <if test="priority != null">
        priority = #{priority,jdbcType=INTEGER},
      </if>
      <if test="indicatorType != null">
        indicator_type = #{indicatorType,jdbcType=INTEGER},
      </if>
      <if test="indicatorName != null">
        indicator_name = #{indicatorName,jdbcType=VARCHAR},
      </if>
      <if test="targetDate != null">
        target_date = #{targetDate,jdbcType=DATE},
      </if>
      <if test="businessType != null">
        business_type = #{businessType,jdbcType=INTEGER},
      </if>
      <if test="metricsMethod != null">
        metrics_method = #{metricsMethod,jdbcType=INTEGER},
      </if>
      <if test="categoryName != null">
        category_name = #{categoryName,jdbcType=VARCHAR},
      </if>
      <if test="sku != null">
        sku = #{sku,jdbcType=VARCHAR},
      </if>
      <if test="indicatorExpectedValue != null">
        indicator_expected_value = #{indicatorExpectedValue,jdbcType=DECIMAL},
      </if>
      <if test="indicatorStatus != null">
        indicator_status = #{indicatorStatus,jdbcType=INTEGER},
      </if>
      <if test="indicatorCurrentValue != null">
        indicator_current_value = #{indicatorCurrentValue,jdbcType=DECIMAL},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="list" parameterType="net.summerfarm.crm.model.entity.BDVisit.BdVisitTargetIndicatorQuery" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from crm_bd_visit_target_indicator
    <where>
      <if test="id != null">
        and id = #{id,jdbcType=BIGINT}
      </if>
      <if test="cityManagerVisitTargetId != null">
        and city_manager_visit_target_id = #{cityManagerVisitTargetId,jdbcType=BIGINT}
      </if>
      <if test="bdVisitTargetId != null">
        and bd_visit_target_id = #{bdVisitTargetId,jdbcType=BIGINT}
      </if>
      <if test="bdId != null">
        and bd_id = #{bdId,jdbcType=INTEGER}
      </if>
      <if test="bdName != null and bdName != ''">
        and bd_name = #{bdName,jdbcType=VARCHAR}
      </if>
      <if test="priority != null">
        and priority = #{priority,jdbcType=INTEGER}
      </if>
      <if test="indicatorType != null">
        and indicator_type = #{indicatorType,jdbcType=INTEGER}
      </if>
      <if test="indicatorName != null and indicatorName != ''">
        and indicator_name = #{indicatorName,jdbcType=VARCHAR}
      </if>
      <if test="targetDate != null">
        and target_date = #{targetDate,jdbcType=DATE}
      </if>
      <if test="businessType != null">
        and business_type = #{businessType,jdbcType=INTEGER}
      </if>
      <if test="metricsMethod != null">
        and metrics_method = #{metricsMethod,jdbcType=INTEGER}
      </if>
      <if test="categoryName != null and categoryName != ''">
        and category_name = #{categoryName,jdbcType=VARCHAR}
      </if>
      <if test="sku != null and sku != ''">
        and sku = #{sku,jdbcType=VARCHAR}
      </if>
      <if test="indicatorExpectedValue != null">
        and indicator_expected_value = #{indicatorExpectedValue,jdbcType=DECIMAL}
      </if>
      <if test="indicatorStatus != null">
        and indicator_status = #{indicatorStatus,jdbcType=INTEGER}
      </if>
      <if test="indicatorCurrentValue != null">
        and indicator_current_value = #{indicatorCurrentValue,jdbcType=DECIMAL}
      </if>
      <if test="createTime != null">
        and create_time = #{createTime,jdbcType=TIMESTAMP}
      </if>
      <if test="updateTime != null">
        and update_time = #{updateTime,jdbcType=TIMESTAMP}
      </if>
    </where>
    order by id desc
  </select>

  <!-- 批量更新指标当前值和状态 -->
  <update id="batchUpdateIndicatorValueAndStatus">
    <foreach collection="list" item="item" separator=";">
      UPDATE crm_bd_visit_target_indicator 
      SET 
        actual_value = #{item.indicatorCurrentValue,jdbcType=DECIMAL},
        indicator_status = 
          CASE 
            WHEN #{item.indicatorCurrentValue,jdbcType=DECIMAL} >= (
              SELECT target_value FROM crm_bd_visit_target_indicator WHERE id = #{item.bdVisitTargetIndicatorId,jdbcType=BIGINT}
            ) THEN 1
            ELSE 0
          END,
        update_time = NOW()
      WHERE id = #{item.bdVisitTargetIndicatorId,jdbcType=BIGINT}
        AND #{item.indicatorCurrentValue,jdbcType=DECIMAL} IS NOT NULL
    </foreach>
  </update>

</mapper>