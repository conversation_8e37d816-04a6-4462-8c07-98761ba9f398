<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.crm.mapper.manage.BDVisit.BdVisitTargetIndicatorConfigMapper">
  
  <resultMap id="BaseResultMap" type="net.summerfarm.crm.model.domain.BDVisit.BdVisitTargetIndicatorConfig">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="indicator_type" jdbcType="VARCHAR" property="indicatorType" />
    <result column="indicator_name" jdbcType="VARCHAR" property="indicatorName" />
    <result column="indicator_description" jdbcType="VARCHAR" property="indicatorDescription" />
    <result column="indicator_unit" jdbcType="VARCHAR" property="indicatorUnit" />
    <result column="is_active" jdbcType="TINYINT" property="isActive" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>

  <sql id="Base_Column_List">
    id, indicator_type, indicator_name, indicator_description, indicator_unit, is_active, creator, create_time, update_time
  </sql>

  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from crm_bd_visit_target_indicator_config
    where id = #{id,jdbcType=BIGINT}
  </select>

  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from crm_bd_visit_target_indicator_config
    where id = #{id,jdbcType=BIGINT}
  </delete>

  <insert id="insertBatch" parameterType="java.util.List" useGeneratedKeys="true" keyProperty="id">
    insert into crm_bd_visit_target_indicator_config (indicator_type, indicator_name, indicator_description,
    indicator_unit, is_active, creator)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.indicatorType,jdbcType=VARCHAR}, #{item.indicatorName,jdbcType=VARCHAR}, #{item.indicatorDescription,jdbcType=VARCHAR},
      #{item.indicatorUnit,jdbcType=VARCHAR}, #{item.isActive,jdbcType=TINYINT}, #{item.creator,jdbcType=VARCHAR})
    </foreach>
  </insert>

  <insert id="insertSelective" parameterType="net.summerfarm.crm.model.domain.BDVisit.BdVisitTargetIndicatorConfig" useGeneratedKeys="true" keyProperty="id">
    insert into crm_bd_visit_target_indicator_config
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="indicatorType != null">
        indicator_type,
      </if>
      <if test="indicatorName != null">
        indicator_name,
      </if>
      <if test="indicatorDescription != null">
        indicator_description,
      </if>
      <if test="indicatorUnit != null">
        indicator_unit,
      </if>
      <if test="isActive != null">
        is_active,
      </if>
      <if test="creator != null">
        creator,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="indicatorType != null">
        #{indicatorType,jdbcType=VARCHAR},
      </if>
      <if test="indicatorName != null">
        #{indicatorName,jdbcType=VARCHAR},
      </if>
      <if test="indicatorDescription != null">
        #{indicatorDescription,jdbcType=VARCHAR},
      </if>
      <if test="indicatorUnit != null">
        #{indicatorUnit,jdbcType=VARCHAR},
      </if>
      <if test="isActive != null">
        #{isActive,jdbcType=TINYINT},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>

  <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.crm.model.entity.BDVisit.BdVisitTargetIndicatorConfigUpdate">
    update crm_bd_visit_target_indicator_config
    <set>
      <if test="indicatorType != null">
        indicator_type = #{indicatorType,jdbcType=VARCHAR},
      </if>
      <if test="indicatorName != null">
        indicator_name = #{indicatorName,jdbcType=VARCHAR},
      </if>
      <if test="indicatorDescription != null">
        indicator_description = #{indicatorDescription,jdbcType=VARCHAR},
      </if>
      <if test="indicatorUnit != null">
        indicator_unit = #{indicatorUnit,jdbcType=VARCHAR},
      </if>
      <if test="isActive != null">
        is_active = #{isActive,jdbcType=TINYINT},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="list" parameterType="net.summerfarm.crm.model.entity.BDVisit.BdVisitTargetIndicatorConfigQuery" resultMap="BaseResultMap">
    SELECT
    <include refid="Base_Column_List" />
    FROM crm_bd_visit_target_indicator_config
    <where>
      <if test="id != null">
        AND id = #{id,jdbcType=BIGINT}
      </if>
      <if test="indicatorType != null and indicatorType != ''">
        AND indicator_type = #{indicatorType,jdbcType=VARCHAR}
      </if>
      <if test="indicatorName != null and indicatorName != ''">
        AND indicator_name = #{indicatorName,jdbcType=VARCHAR}
      </if>
      <if test="indicatorDescription != null and indicatorDescription != ''">
        AND indicator_description = #{indicatorDescription,jdbcType=VARCHAR}
      </if>
      <if test="indicatorUnit != null and indicatorUnit != ''">
        AND indicator_unit = #{indicatorUnit,jdbcType=VARCHAR}
      </if>
      <if test="isActive != null">
        AND is_active = #{isActive,jdbcType=TINYINT}
      </if>
      <if test="creator != null and creator != ''">
        AND creator = #{creator,jdbcType=VARCHAR}
      </if>
      <if test="createTime != null">
        AND create_time = #{createTime,jdbcType=TIMESTAMP}
      </if>
      <if test="updateTime != null">
        AND update_time = #{updateTime,jdbcType=TIMESTAMP}
      </if>
    </where>
    ORDER BY id DESC
  </select>

</mapper>