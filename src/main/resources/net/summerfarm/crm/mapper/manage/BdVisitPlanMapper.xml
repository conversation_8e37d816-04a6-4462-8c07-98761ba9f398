<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.crm.mapper.manage.BDVisit.BdVisitPlanMapper">
  
  <resultMap id="BaseResultMap" type="net.summerfarm.crm.model.domain.BDVisit.BdVisitPlan">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="sales_id" jdbcType="BIGINT" property="salesId" />
    <result column="plan_date" jdbcType="DATE" property="planDate" />
    <result column="plan_type" jdbcType="VARCHAR" property="planType" />
    <result column="plan_description" jdbcType="VARCHAR" property="planDescription" />
    <result column="plan_status" jdbcType="VARCHAR" property="planStatus" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>

  <sql id="Base_Column_List">
    id, sales_id, plan_date, plan_type, plan_description, plan_status, creator, create_time, update_time
  </sql>

  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from crm_bd_visit_plan
    where id = #{id,jdbcType=BIGINT}
  </select>

  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from crm_bd_visit_plan
    where id = #{id,jdbcType=BIGINT}
  </delete>

  <insert id="insertBatch" parameterType="java.util.List" useGeneratedKeys="true" keyProperty="id">
    insert into crm_bd_visit_plan (bd_visit_target_id, sales_id, plan_date,
    plan_description, plan_status, creator)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.bdVisitTargetId,jdbcType=BIGINT}, #{item.salesId,jdbcType=BIGINT}, #{item.planDate,jdbcType=DATE},
      #{item.planDescription,jdbcType=VARCHAR}, #{item.planStatus,jdbcType=INTEGER}, #{item.creator,jdbcType=VARCHAR})
    </foreach>
  </insert>

  <insert id="insertSelective" parameterType="net.summerfarm.crm.model.domain.BDVisit.BdVisitPlan" useGeneratedKeys="true" keyProperty="id">
    insert into crm_bd_visit_plan
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="salesId != null">
        sales_id,
      </if>
      <if test="planDate != null">
        plan_date,
      </if>
      <if test="planType != null">
        plan_type,
      </if>
      <if test="planDescription != null">
        plan_description,
      </if>
      <if test="planStatus != null">
        plan_status,
      </if>
      <if test="creator != null">
        creator,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="salesId != null">
        #{salesId,jdbcType=BIGINT},
      </if>
      <if test="planDate != null">
        #{planDate,jdbcType=DATE},
      </if>
      <if test="planType != null">
        #{planType,jdbcType=VARCHAR},
      </if>
      <if test="planDescription != null">
        #{planDescription,jdbcType=VARCHAR},
      </if>
      <if test="planStatus != null">
        #{planStatus,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>

  <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.crm.model.entity.BDVisit.BdVisitPlanUpdate">
    update crm_bd_visit_plan
    <set>
      <if test="salesId != null">
        sales_id = #{salesId,jdbcType=BIGINT},
      </if>
      <if test="planDate != null">
        plan_date = #{planDate,jdbcType=DATE},
      </if>
      <if test="planType != null">
        plan_type = #{planType,jdbcType=VARCHAR},
      </if>
      <if test="planDescription != null">
        plan_description = #{planDescription,jdbcType=VARCHAR},
      </if>
      <if test="planStatus != null">
        plan_status = #{planStatus,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>

  <!-- 根据查询条件查询记录列表 -->
  <select id="list" parameterType="net.summerfarm.crm.model.entity.BDVisit.BdVisitPlanQuery" resultMap="BaseResultMap">
    SELECT
    <include refid="Base_Column_List" />
    FROM crm_bd_visit_plan
    <where>
      <if test="id != null">
        AND id = #{id,jdbcType=BIGINT}
      </if>
      <if test="salesId != null">
        AND sales_id = #{salesId,jdbcType=BIGINT}
      </if>
      <if test="planDate != null">
        AND plan_date = #{planDate,jdbcType=DATE}
      </if>
      <if test="planDescription != null and planDescription != ''">
        AND plan_description = #{planDescription,jdbcType=VARCHAR}
      </if>
      <if test="planStatus != null and planStatus != ''">
        AND plan_status = #{planStatus,jdbcType=VARCHAR}
      </if>
      <if test="creator != null and creator != ''">
        AND creator = #{creator,jdbcType=VARCHAR}
      </if>
      <if test="createTime != null">
        AND create_time = #{createTime,jdbcType=TIMESTAMP}
      </if>
      <if test="updateTime != null">
        AND update_time = #{updateTime,jdbcType=TIMESTAMP}
      </if>
    </where>
    ORDER BY id DESC
  </select>

</mapper>