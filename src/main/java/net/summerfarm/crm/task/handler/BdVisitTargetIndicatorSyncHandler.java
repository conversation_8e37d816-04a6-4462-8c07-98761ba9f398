package net.summerfarm.crm.task.handler;


import net.summerfarm.crm.mapper.repository.BDVisit.BdVisitTargetIndicatorRepository;
import net.summerfarm.crm.service.BDVisit.BdVisitTargetIndicatorService;
import net.summerfarm.crm.service.BDVisit.BdVisitTargetIndicatorSyncService;
import net.summerfarm.crm.service.DataSynchronization.DataSynchronizationInformationService;
import net.summerfarm.crm.model.domain.BDVisit.BdVisitTargetIndicatorSync;

import org.springframework.stereotype.Component;

import com.alibaba.schedulerx.worker.processor.ProcessResult;

import lombok.extern.slf4j.Slf4j;
import net.xianmu.task.process.XianMuJavaProcessorV2;
import net.xianmu.task.vo.input.XmJobInput;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.List;


/**
 * 拜访目标指标同步处理
 *
 * <AUTHOR>
 * @date 2025-09-08 10:00:00
 */
@Slf4j
@Component
public class BdVisitTargetIndicatorSyncHandler extends XianMuJavaProcessorV2 {
    
    @Resource
    private DataSynchronizationInformationService dataSynchronizationInformationService;
    
    @Resource
    private BdVisitTargetIndicatorSyncService bdVisitTargetIndicatorSyncService;
    
    @Resource
    private BdVisitTargetIndicatorService bdVisitTargetIndicatorService;

    @Resource
    private BdVisitTargetIndicatorRepository bdVisitTargetIndicatorRepository;
    
    private static final String SYNC_TABLE_NAME = "crm_bd_visit_target_indicator_sync";
    private static final int PAGE_SIZE = 1000;
    
    @Override
    public ProcessResult processResult(XmJobInput xmJobInput) throws Exception {
        log.info("开始执行销售拜访目标指标同步任务");
        
        try {
            // 步骤1: 读取同步表标识位，检查是否已同步
            if (!checkSyncStatus()) {
                log.info("同步表数据未准备好，跳过本次同步");
                return new ProcessResult(true);
            }
            
            // 步骤2: 分页读取同步表数据并更新指标当前值
            processSyncData();
            
            log.info("销售拜访目标指标同步任务执行完成");
            return new ProcessResult(true);
            
        } catch (Exception e) {
            log.error("销售拜访目标指标同步任务执行失败", e);
            return new ProcessResult(false, "同步任务执行失败: " + e.getMessage());
        }
    }
    
    /**
     * 检查同步状态
     * @return true-可以同步，false-不能同步
     */
    private boolean checkSyncStatus() {
        try {
            // 生成当前小时级别的时间标识（yyyyMMddhh格式）
            String currentHourFlag = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd")) + 
                    String.format("%02d", LocalTime.now().getHour());
            
            // 使用Service层检查同步状态，传入时间标识
            boolean needSync = dataSynchronizationInformationService.needSync(SYNC_TABLE_NAME, currentHourFlag);
            if (!needSync) {
                log.info("同步表{}在时间标识{}已同步，跳过同步", SYNC_TABLE_NAME, currentHourFlag);
                return false;
            }
            
            log.info("同步表{}状态检查通过，需要同步，时间标识: {}", SYNC_TABLE_NAME, currentHourFlag);
            return true;
            
        } catch (Exception e) {
            log.error("检查同步状态失败", e);
            return false;
        }
    }
    
    /**
     * 分页处理同步数据
     */
    private void processSyncData() {
        int pageNum = 1;
        boolean hasMore = true;
        int totalProcessed = 0;
        
        while (hasMore) {
            try {
                // 分页查询同步表数据
                int offset = (pageNum - 1) * PAGE_SIZE;
                List<BdVisitTargetIndicatorSync> syncList = bdVisitTargetIndicatorSyncService.selectByPage(offset, PAGE_SIZE);
                
                if (syncList == null || syncList.isEmpty()) {
                    hasMore = false;
                    break;
                }
                
                // 批量更新当前页数据
                batchUpdateTargetIndicators(syncList);
                totalProcessed += syncList.size();
                
                log.info("已处理第{}页数据，本页记录数: {}", pageNum, syncList.size());
                
                // 检查是否还有更多数据
                hasMore = syncList.size() == PAGE_SIZE;
                pageNum++;
                
            } catch (Exception e) {
                log.error("处理第{}页同步数据失败", pageNum, e);
                throw e;
            }
        }
        
        log.info("同步数据处理完成，总共处理记录数: {}", totalProcessed);
    }
    
    /**
     * 批量更新目标指标当前值和完成状态
     * @param syncDataList 同步数据列表
     */
    private void batchUpdateTargetIndicators(List<BdVisitTargetIndicatorSync> syncDataList) {
        try {
            if (syncDataList == null || syncDataList.isEmpty()) {
                return;
            }
            
            // 调用Repository层执行批量更新
            int updateCount = bdVisitTargetIndicatorRepository.batchUpdateIndicatorValueAndStatus(syncDataList);
            
            log.info("批量更新目标指标完成，本批次数据: {}, 更新记录数: {}", 
                    syncDataList.size(), updateCount);
            
        } catch (Exception e) {
            log.error("批量更新目标指标失败", e);
            throw e;
        }
    }
    

}
