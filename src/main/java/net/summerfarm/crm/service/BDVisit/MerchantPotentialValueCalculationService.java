package net.summerfarm.crm.service.BDVisit;

import net.summerfarm.crm.model.domain.BDVisit.BdVisitPlanIndicator;
import net.summerfarm.crm.model.domain.BDVisit.BdVisitPlanIndicatorSync;
import net.summerfarm.crm.model.domain.BDVisit.BdVisitTargetIndicator;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 门店潜力值计算服务接口
 *
 * <AUTHOR>
 * @date 2025-09-08
 */
public interface MerchantPotentialValueCalculationService {

    /**
     * 计算门店综合潜力值
     * 根据目标指标优先级和拜访系数计算最终潜力值
     *
     * @param merchantId 门店ID
     * @param planIndicators 拜访计划指标列表
     * @param targetIndicators 拜访目标指标列表
     * @return 综合潜力值
     */
    BigDecimal calculateComprehensivePotentialValue(Long merchantId, 
                                                   List<BdVisitPlanIndicator> planIndicators,
                                                   List<BdVisitTargetIndicator> targetIndicators);

    /**
     * 批量计算门店综合潜力值
     *
     * @param merchantIds 门店ID列表
     * @return 门店ID和潜力值的映射
     */
    Map<Long, BigDecimal> batchCalculateComprehensivePotentialValue(List<Long> merchantIds);

    /**
     * 批量计算门店综合潜力值（基于同步数据）
     *
     * @param syncDataList 拜访计划指标同步数据列表
     * @return 门店ID和潜力值的映射
     */
    Map<Long, BigDecimal> batchCalculateComprehensivePotentialValueFromSyncData(List<BdVisitPlanIndicatorSync> syncDataList);

    /**
     * 计算目标权重
     * 将总权重的80%分配给前三个优先级（P1, P2, P3）的目标
     * 将总权重的20%平均分配给P3之后的所有其他目标
     *
     * @param targetIndicators 目标指标列表
     * @return 目标指标ID和权重的映射
     */
    Map<Long, BigDecimal> calculateTargetWeights(List<BdVisitTargetIndicator> targetIndicators);

    /**
     * 获取门店拜访系数
     * 近七天拜访过为0.2，否则为1
     *
     * @param merchantId 门店ID
     * @return 拜访系数
     */
    BigDecimal getMerchantVisitCoefficient(Long merchantId);

    /**
     * 批量获取门店拜访系数
     *
     * @param merchantIds 门店ID列表
     * @return 门店ID和拜访系数的映射
     */
    Map<Long, BigDecimal> batchGetMerchantVisitCoefficient(List<Long> merchantIds);
}