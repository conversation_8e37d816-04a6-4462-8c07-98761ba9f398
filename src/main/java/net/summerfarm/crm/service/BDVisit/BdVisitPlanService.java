package net.summerfarm.crm.service.BDVisit;

import net.summerfarm.crm.model.input.BDVisit.GenerateVisitPlanDraftInput;

/**
 * 销售拜访计划服务接口
 *
 * <AUTHOR>
 * @date 2025-01-14
 */
public interface BdVisitPlanService {

    /**
     * 生成销售拜访计划草案
     * 根据输入参数生成当天的拜访计划和拜访计划指标
     *
     * @param input 生成拜访计划草案的输入参数
     * @return 是否生成成功
     */
    Boolean generateVisitPlanDraft(GenerateVisitPlanDraftInput input);

}