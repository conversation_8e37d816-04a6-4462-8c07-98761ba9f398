package net.summerfarm.crm.service.BDVisit;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 门店潜力值ES更新服务接口
 *
 * <AUTHOR>
 * @date 2025-09-08
 */
public interface MerchantPotentialValueEsUpdateService {

    /**
     * 批量更新门店潜力值到ES
     *
     * @param merchantPotentialMap 门店ID和潜力值的映射
     * @return 更新成功的门店数量
     */
    int batchUpdateMerchantPotentialValueToEs(Map<Long, BigDecimal> merchantPotentialMap);

    /**
     * 更新单个门店潜力值到ES
     *
     * @param merchantId 门店ID
     * @param potentialValue 潜力值
     * @return 是否更新成功
     */
    boolean updateMerchantPotentialValueToEs(Long merchantId, BigDecimal potentialValue);

    /**
     * 批量更新门店潜力值到ES（通过门店ID列表）
     *
     * @param merchantIds 门店ID列表
     * @return 更新成功的门店数量
     */
    int batchUpdateMerchantPotentialValueToEsByIds(List<Long> merchantIds);
}