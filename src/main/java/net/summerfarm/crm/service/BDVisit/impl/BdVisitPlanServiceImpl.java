package net.summerfarm.crm.service.BDVisit.impl;

import lombok.extern.slf4j.Slf4j;
import net.summerfarm.crm.common.base.BaseService;

import net.summerfarm.crm.mapper.repository.BDVisit.BdVisitPlanIndicatorRepository;
import net.summerfarm.crm.mapper.repository.BDVisit.BdVisitPlanRepository;
import net.summerfarm.crm.mapper.repository.BDVisit.BdVisitTargetRepository;
import net.summerfarm.crm.facade.ContactQueryFacade;
import net.summerfarm.crm.model.dto.ContactDto;
import net.summerfarm.crm.model.domain.BDVisit.BdVisitPlan;
import net.summerfarm.crm.model.domain.BDVisit.BdVisitTarget;
import net.summerfarm.crm.model.domain.BDVisit.BdVisitTargetIndicator;
import net.summerfarm.crm.model.domain.BDVisit.BdVisitPlanIndicator;
import net.summerfarm.crm.model.domain.BDVisit.BdVisitPlanIndicatorSync;
import net.summerfarm.crm.model.entity.BDVisit.BdVisitPlanQuery;
import net.summerfarm.crm.model.entity.BDVisit.BdVisitTargetIndicatorQuery;
import net.summerfarm.crm.model.input.BDVisit.GenerateVisitPlanDraftInput;
import net.summerfarm.crm.model.input.BDVisit.VisitMerchantInfoInput;
import net.summerfarm.crm.service.BDVisit.BdVisitPlanService;
import net.summerfarm.crm.service.BDVisit.BdVisitTargetIndicatorService;
import net.summerfarm.crm.service.BDVisit.BdVisitPlanIndicatorSyncService;
import net.summerfarm.crm.enums.BDVisit.BdVisitPlanEnum;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 销售拜访计划服务实现类
 *
 * <AUTHOR>
 * @date 2025-01-14
 */
@Slf4j
@Service
public class BdVisitPlanServiceImpl extends BaseService implements BdVisitPlanService {

    // ==================== 常量定义 ====================
    
    /** 拜访类型：线下拜访 */
    private static final Integer VISIT_TYPE_OFFLINE = 0;
    
    /** 拜访类型：线上拜访 */
    private static final Integer VISIT_TYPE_ONLINE = 1;
    
    /** 有效联系人ID的最小值 */
    private static final Long VALID_CONTACT_ID_MIN = 0L;
    
    /** 日志消息模板 */
    private static final String LOG_DUPLICATE_OFFLINE_MERCHANTS = "销售{}在{}已存在重复的线下拜访门店: {}";
    private static final String LOG_DUPLICATE_ONLINE_MERCHANTS = "销售{}在{}已存在重复的线上拜访门店: {}";
    private static final String LOG_CONTACT_INFO_FOUND = "为拜访计划{}找到联系人信息: 联系人ID={}, 姓名={}, 手机号={}";
    private static final String LOG_CONTACT_NOT_FOUND = "未找到联系人ID为{}的联系人信息";
    
    /** 异常消息模板 */
    private static final String ERROR_BD_ID_NULL = "销售bdId不能为空";
    private static final String ERROR_VISIT_TARGET_NOT_FOUND = "未找到对应的拜访目标";
    private static final String ERROR_GENERATE_PLAN_FAILED = "生成销售拜访计划草案失败";
    private static final String ERROR_QUERY_EXISTING_PLANS_FAILED = "查询已存在的拜访计划失败";
    private static final String ERROR_GET_TARGET_INDICATORS_FAILED = "读取拜访目标指标失败";
    private static final String ERROR_GET_SYNC_DATA_FAILED = "读取拜访私海目标指标同步数据失败";
    private static final String ERROR_GENERATE_PLAN_INDICATORS_FAILED = "生成拜访计划指标失败";
    private static final String ERROR_INSERT_PLAN_INDICATORS_FAILED = "批量插入拜访计划指标失败";
    
    // ==================== 依赖注入 ====================

    @Resource
    private BdVisitPlanRepository bdVisitPlanRepository;

    @Resource
    private BdVisitTargetRepository bdVisitTargetRepository;
    
    @Resource
    private BdVisitTargetIndicatorService bdVisitTargetIndicatorService;
    
    @Resource
    private BdVisitPlanIndicatorRepository bdVisitPlanIndicatorRepository;
    
    @Resource
    private BdVisitPlanIndicatorSyncService bdVisitPlanIndicatorSyncService;
    
    @Resource
    private ContactQueryFacade contactQueryFacade;
    



    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean generateVisitPlanDraft(GenerateVisitPlanDraftInput input) {
        log.info("开始生成销售拜访计划草案，输入参数: {}", input);
        
        try {
            // 1. 验证并获取基础参数
            Integer bdId = validateAndGetBdId(input);
            LocalDate visitDate = LocalDate.now();
            
            // 2. 获取拜访目标
            BdVisitTarget visitTarget = getVisitTarget(bdId, visitDate);
            
            // 3. 检查门店重复性
            if (checkDuplicateMerchants(input, bdId, visitDate)) {
                return true; // 存在重复门店，直接返回成功
            }
            
            // 4. 准备指标数据
            List<BdVisitTargetIndicator> targetIndicators = getVisitTargetIndicators(visitTarget.getId());
            List<BdVisitPlanIndicatorSync> indicatorSyncData = getVisitPlanIndicatorSyncData(targetIndicators);
            
            // 5. 预先获取所有门店的联系人信息映射
            Map<Long, ContactDto> contactMap = getContactMapFromInput(input);
            
            // 6. 生成并保存拜访计划
            List<BdVisitPlan> visitPlans = generateVisitPlansFromTarget(visitTarget, input, bdId, visitDate, contactMap);
            int insertCount = saveVisitPlans(visitPlans);
            
            // 7. 生成并保存拜访计划指标
            saveVisitPlanIndicators(visitPlans, targetIndicators, input, indicatorSyncData);
            
            return insertCount > 0;
            
        } catch (Exception e) {
            log.error(ERROR_GENERATE_PLAN_FAILED, e);
            throw e;
        }
    }
    
    /**
     * 获取已存在的拜访计划
     */
    private List<BdVisitPlan> getExistingVisitPlans(Integer bdId, LocalDate visitDate) {
        try {
            BdVisitPlanQuery query = new BdVisitPlanQuery();
            query.setBdId(bdId);
            query.setVisitDate(visitDate);
            return bdVisitPlanRepository.list(query);
        } catch (Exception e) {
            log.error(ERROR_QUERY_EXISTING_PLANS_FAILED + "，bdId: {}, visitDate: {}", bdId, visitDate, e);
            return new ArrayList<>();
        }
    }
    
    /**
     * 根据输入参数获取所有门店的联系人信息映射
     */
    private Map<Long, ContactDto> getContactMapFromInput(GenerateVisitPlanDraftInput input) {
        // 收集所有contactId
        List<Long> contactIds = new ArrayList<>();
        
        if (!CollectionUtils.isEmpty(input.getVisitOfflineMIdList())) {
            input.getVisitOfflineMIdList().stream()
                    .map(VisitMerchantInfoInput::getContactId)
                    .filter(contactId -> contactId != null && contactId > VALID_CONTACT_ID_MIN)
                    .forEach(contactIds::add);
        }
        
        if (!CollectionUtils.isEmpty(input.getVisitOnlineMIdList())) {
            input.getVisitOnlineMIdList().stream()
                    .map(VisitMerchantInfoInput::getContactId)
                    .filter(contactId -> contactId != null && contactId > VALID_CONTACT_ID_MIN)
                    .forEach(contactIds::add);
        }
        
        if (CollectionUtils.isEmpty(contactIds)) {
            log.warn("输入参数中没有有效的联系人ID");
            return new HashMap<>();
        }
        
        // 去重
        List<Long> distinctContactIds = contactIds.stream().distinct().collect(Collectors.toList());
        
        // 批量查询联系人信息
        List<ContactDto> contactList = contactQueryFacade.getMerchantContactList(distinctContactIds);
        
        // 构建联系人ID到联系人信息的映射
        return contactList.stream()
                .collect(Collectors.toMap(ContactDto::getContactId, contact -> contact, (existing, replacement) -> existing));
    }
    
    /**
     * 根据拜访目标生成拜访计划草案
     */
    private List<BdVisitPlan> generateVisitPlansFromTarget(BdVisitTarget visitTarget, 
                                                          GenerateVisitPlanDraftInput input,
                                                          Integer bdId, 
                                                          LocalDate visitDate,
                                                          Map<Long, ContactDto> contactMap) {
        List<BdVisitPlan> visitPlans = new ArrayList<>();
        
        // 根据拜访目标生成线下拜访计划
        if (!CollectionUtils.isEmpty(input.getVisitOfflineMIdList())) {
            List<BdVisitPlan> offlinePlans = generateOfflineVisitPlans(visitTarget, input, bdId, visitDate, contactMap);
            visitPlans.addAll(offlinePlans);
        }
        
        // 根据拜访目标生成线上拜访计划
        if (!CollectionUtils.isEmpty(input.getVisitOnlineMIdList())) {
            List<BdVisitPlan> onlinePlans = generateOnlineVisitPlans(visitTarget, input, bdId, visitDate, contactMap);
            visitPlans.addAll(onlinePlans);
        }
        
        return visitPlans;
    }
    
    // ==================== 参数验证与基础数据获取 ====================
    
    /**
     * 验证并获取销售bdId
     */
    private Integer validateAndGetBdId(GenerateVisitPlanDraftInput input) {
        Integer bdId = super.getAdminId() != null ? super.getAdminId() : input.getBdId();
        if (bdId == null) {
            log.warn("无法获取销售bdId，生成拜访计划草案失败");
            throw new IllegalArgumentException(ERROR_BD_ID_NULL);
        }
        return bdId;
    }
    
    /**
     * 获取拜访目标
     */
    private BdVisitTarget getVisitTarget(Integer bdId, LocalDate visitDate) {
        List<BdVisitTarget> visitTargets = bdVisitTargetRepository.selectByBdIdAndTargetDate(bdId, visitDate);
        if (CollectionUtils.isEmpty(visitTargets)) {
            log.warn("未找到销售{}在{}的拜访目标，无法生成拜访计划草案", bdId, visitDate);
            throw new IllegalArgumentException(ERROR_VISIT_TARGET_NOT_FOUND);
        }
        return visitTargets.get(0);
    }
    

    
    // ==================== 门店重复性检查 ====================
    
    /**
     * 检查门店重复性
     */
    private boolean checkDuplicateMerchants(GenerateVisitPlanDraftInput input, Integer bdId, LocalDate visitDate) {
        List<BdVisitPlan> existingPlans = getExistingVisitPlans(bdId, visitDate);
        if (CollectionUtils.isEmpty(existingPlans)) {
            return false;
        }
        
        // 获取已存在拜访计划中的门店ID列表
        List<Long> existingMerchantIds = existingPlans.stream()
                .map(BdVisitPlan::getMId)
                .collect(Collectors.toList());
        
        // 检查线下拜访门店重复性
        if (checkOfflineMerchantDuplicates(input.getVisitOfflineMIdList(), existingMerchantIds, bdId, visitDate)) {
            return true;
        }
        
        // 检查线上拜访门店重复性
        return checkOnlineMerchantDuplicates(input.getVisitOnlineMIdList(), existingMerchantIds, bdId, visitDate);
    }
    
    /**
     * 检查线下拜访门店重复性
     */
    private boolean checkOfflineMerchantDuplicates(List<VisitMerchantInfoInput> inputOfflineMerchants, 
                                                   List<Long> existingMerchantIds, Integer bdId, LocalDate visitDate) {
        if (CollectionUtils.isEmpty(inputOfflineMerchants)) {
            return false;
        }
        
        List<Long> inputOfflineMIds = inputOfflineMerchants.stream()
                .map(VisitMerchantInfoInput::getMId)
                .collect(Collectors.toList());
        List<Long> duplicateOfflineMIds = inputOfflineMIds.stream()
                .filter(existingMerchantIds::contains)
                .collect(Collectors.toList());
        
        if (!CollectionUtils.isEmpty(duplicateOfflineMIds)) {
            log.info(LOG_DUPLICATE_OFFLINE_MERCHANTS, bdId, visitDate, duplicateOfflineMIds);
            return true;
        }
        return false;
    }
    
    /**
     * 检查线上拜访门店重复性
     */
    private boolean checkOnlineMerchantDuplicates(List<VisitMerchantInfoInput> inputOnlineMerchants, 
                                                  List<Long> existingMerchantIds, Integer bdId, LocalDate visitDate) {
        if (CollectionUtils.isEmpty(inputOnlineMerchants)) {
            return false;
        }
        
        List<Long> inputOnlineMIds = inputOnlineMerchants.stream()
                .map(VisitMerchantInfoInput::getMId)
                .collect(Collectors.toList());
        List<Long> duplicateOnlineMIds = inputOnlineMIds.stream()
                .filter(existingMerchantIds::contains)
                .collect(Collectors.toList());
        
        if (!CollectionUtils.isEmpty(duplicateOnlineMIds)) {
            log.info(LOG_DUPLICATE_ONLINE_MERCHANTS, bdId, visitDate, duplicateOnlineMIds);
            return true;
        }
        return false;
    }
    
    // ==================== 拜访计划生成与保存 ====================
    
    /**
     * 保存拜访计划
     */
    private int saveVisitPlans(List<BdVisitPlan> visitPlans) {
        if (CollectionUtils.isEmpty(visitPlans)) {
            log.warn("未生成任何拜访计划");
            return 0;
        }
        
        int insertCount = bdVisitPlanRepository.insertBatch(visitPlans);
        log.info("成功生成{}条拜访计划草案", insertCount);
        return insertCount;
    }
    
    /**
     * 保存拜访计划指标
     */
    private void saveVisitPlanIndicators(List<BdVisitPlan> visitPlans, 
                                        List<BdVisitTargetIndicator> targetIndicators, 
                                        GenerateVisitPlanDraftInput input, 
                                        List<BdVisitPlanIndicatorSync> indicatorSyncData) {
        if (CollectionUtils.isEmpty(visitPlans) || CollectionUtils.isEmpty(targetIndicators)) {
            return;
        }
        
        log.info("读取到{}条拜访目标指标", targetIndicators.size());
        log.info("读取到{}条拜访私海目标指标同步数据", indicatorSyncData != null ? indicatorSyncData.size() : 0);
        
        // 将indicatorSyncData转换为Map以提升查找性能
        Map<String, BdVisitPlanIndicatorSync> syncDataMap = buildSyncDataMap(indicatorSyncData);
        List<BdVisitPlanIndicator> planIndicators = generateVisitPlanIndicators(visitPlans, targetIndicators, input, syncDataMap);
        
        if (!CollectionUtils.isEmpty(planIndicators)) {
            int indicatorInsertCount = insertPlanIndicatorsBatch(planIndicators);
            log.info("成功创建{}条拜访计划指标", indicatorInsertCount);
        }
    }
    
    /**
     * 生成线下拜访计划
     */
    private List<BdVisitPlan> generateOfflineVisitPlans(BdVisitTarget target, 
                                                       GenerateVisitPlanDraftInput input,
                                                       Integer bdId, 
                                                       LocalDate visitDate,
                                                       Map<Long, ContactDto> contactMap) {
        List<BdVisitPlan> plans = new ArrayList<>();
        
        // 获取线下拜访门店列表
        List<VisitMerchantInfoInput> offlineMerchants = input.getVisitOfflineMIdList();
        if (CollectionUtils.isEmpty(offlineMerchants)) {
            log.warn("线下拜访门店列表为空，无法生成线下拜访计划");
            return plans;
        }
        
        // 根据拜访数量限制生成拜访计划
        for (VisitMerchantInfoInput merchantInfo : offlineMerchants) {
            BdVisitPlan plan = createVisitPlan(target, bdId, visitDate, merchantInfo, VISIT_TYPE_OFFLINE, input.getTrafficType(), contactMap);
            plans.add(plan);
        }
        
        log.debug("为目标{}生成{}条线下拜访计划", target.getId(), plans.size());
        return plans;
    }
    
    /**
     * 生成线上拜访计划
     */
    private List<BdVisitPlan> generateOnlineVisitPlans(BdVisitTarget target, 
                                                      GenerateVisitPlanDraftInput input,
                                                      Integer bdId, 
                                                      LocalDate visitDate,
                                                      Map<Long, ContactDto> contactMap) {
        List<BdVisitPlan> plans = new ArrayList<>();
        
        // 获取线上拜访门店列表
        List<VisitMerchantInfoInput> onlineMerchants = input.getVisitOnlineMIdList();
        if (CollectionUtils.isEmpty(onlineMerchants)) {
            log.warn("线上拜访门店列表为空，无法生成线上拜访计划");
            return plans;
        }

        for (VisitMerchantInfoInput merchantInfo : onlineMerchants) {
            BdVisitPlan plan = createVisitPlan(target, bdId, visitDate, merchantInfo, VISIT_TYPE_ONLINE, input.getTrafficType(), contactMap);
            plans.add(plan);
        }
        
        log.debug("为目标{}生成{}条线上拜访计划", target.getId(), plans.size());
        return plans;
    }
    
    /**
     * 创建拜访计划对象
     */
    private BdVisitPlan createVisitPlan(BdVisitTarget target, 
                                       Integer bdId, 
                                       LocalDate visitDate, 
                                       VisitMerchantInfoInput merchantInfo, 
                                       Integer visitType,
                                       Integer trafficType,
                                       Map<Long, ContactDto> contactMap) {
        BdVisitPlan plan = new BdVisitPlan();
        
        // 基础信息
        plan.setBdVisitTargetId(target.getId());
        plan.setBdId(bdId);
        plan.setBdName(target.getBdName());
        plan.setVisitDate(visitDate);
        plan.setMId(merchantInfo.getMId());
        plan.setContactId(merchantInfo.getContactId());
        plan.setVisitType(visitType); // 使用常量：VISIT_TYPE_OFFLINE(0)线下拜访，VISIT_TYPE_ONLINE(1)线上拜访
        plan.setVisitStatus(BdVisitPlanEnum.VisitStatus.NOT_VISITED.getCode()); // 未拜访
        
        // 设置联系人信息
        if (merchantInfo.getContactId() != null && merchantInfo.getContactId() > VALID_CONTACT_ID_MIN) {
            ContactDto contact = contactMap.get(merchantInfo.getContactId());
            if (contact != null) {
                plan.setContactId(contact.getContactId());
                plan.setContactName(contact.getContact());
                plan.setContactPhone(contact.getPhone());
                plan.setProvince(contact.getProvince());
                plan.setCity(contact.getCity());
                plan.setArea(contact.getArea());
                plan.setPoi(contact.getPoiNote());
                log.debug(LOG_CONTACT_INFO_FOUND, plan.getId(), contact.getContactId(), contact.getContact(), contact.getPhone());
            } else {
                log.warn(LOG_CONTACT_NOT_FOUND, merchantInfo.getContactId());
            }
        }

        // todo zzq 门店潜力值
        plan.setMerchantPotentialValue(BigDecimal.ZERO);

        // 状态信息
        plan.setStatus(BdVisitPlanEnum.Status.DRAFT.getCode()); // 草稿状态
        plan.setLockStatus(BdVisitPlanEnum.LockStatus.UNLOCKED.getCode()); // 未锁定
        plan.setMerchantOrderStatus(BdVisitPlanEnum.MerchantOrderStatus.NOT_ORDERED.getCode()); // 未下单
        
        // 审计信息
        plan.setCreateTime(LocalDateTime.now());
        plan.setUpdateTime(LocalDateTime.now());
        
        return plan;
    }
    
    /**
     * 读取拜访目标指标
     */
    private List<BdVisitTargetIndicator> getVisitTargetIndicators(Long visitTargetId) {
        try {
            BdVisitTargetIndicatorQuery query = new BdVisitTargetIndicatorQuery();
            query.setBdVisitTargetId(visitTargetId);
            List<BdVisitTargetIndicator> indicators = bdVisitTargetIndicatorService.selectByIds(
                     query.getBdVisitTargetId() != null ? Arrays.asList(query.getBdVisitTargetId()) : new ArrayList<>());
            
            // 使用Mapper的list方法查询
            if (indicators == null || indicators.isEmpty()) {
                // 通过Service查询所有指标，然后过滤
                // 这里需要调用Mapper的list方法
                return new ArrayList<>();
            }
            
            log.debug("读取拜访目标指标，目标ID: {}, 指标数量: {}", visitTargetId, indicators.size());
            return indicators;
        } catch (Exception e) {
            log.error(ERROR_GET_TARGET_INDICATORS_FAILED + "，目标ID: {}", visitTargetId, e);
            return new ArrayList<>();
        }
    }
    
    /**
     * 读取拜访私海目标指标同步数据
     */
    private List<BdVisitPlanIndicatorSync> getVisitPlanIndicatorSyncData(List<BdVisitTargetIndicator> targetIndicators) {
        try {
            if (CollectionUtils.isEmpty(targetIndicators)) {
                return new ArrayList<>();
            }
            
            List<Long> targetIndicatorIds = targetIndicators.stream()
                    .map(BdVisitTargetIndicator::getId)
                    .collect(Collectors.toList());
            
            // 根据目标指标ID查询已存在的拜访计划指标
            List<BdVisitPlanIndicator> existingPlanIndicators = bdVisitPlanIndicatorRepository.selectByTargetIndicatorIds(targetIndicatorIds);
            
            log.debug("读取拜访私海目标指标同步数据，目标指标数量: {}, 已存在计划指标数量: {}", 
                    targetIndicatorIds.size(), existingPlanIndicators != null ? existingPlanIndicators.size() : 0);
            
            // 这里可以根据业务需要返回同步数据，暂时返回空列表
            return new ArrayList<>();
        } catch (Exception e) {
            log.error(ERROR_GET_SYNC_DATA_FAILED, e);
            return new ArrayList<>();
        }
    }
    
    /**
     * 生成拜访计划指标
     */
    private List<BdVisitPlanIndicator> generateVisitPlanIndicators(List<BdVisitPlan> visitPlans, 
                                                                  List<BdVisitTargetIndicator> targetIndicators,
                                                                  GenerateVisitPlanDraftInput input,
                                                                  Map<String, BdVisitPlanIndicatorSync> syncDataMap) {
        List<BdVisitPlanIndicator> planIndicators = new ArrayList<>();
        
        try {
            if (CollectionUtils.isEmpty(visitPlans) || CollectionUtils.isEmpty(targetIndicators)) {
                return planIndicators;
            }
            
            // 获取线下和线上门店ID列表
            List<Long> offlineMerchantIds = new ArrayList<>();
            if (input.getVisitOfflineMIdList() != null) {
                offlineMerchantIds = input.getVisitOfflineMIdList().stream()
                        .map(VisitMerchantInfoInput::getMId)
                        .collect(Collectors.toList());
            }
            
            List<Long> onlineMerchantIds = new ArrayList<>();
            if (input.getVisitOnlineMIdList() != null) {
                onlineMerchantIds = input.getVisitOnlineMIdList().stream()
                        .map(VisitMerchantInfoInput::getMId)
                        .collect(Collectors.toList());
            }
            
            // 为每个拜访计划创建对应的指标
            for (BdVisitPlan visitPlan : visitPlans) {
                for (BdVisitTargetIndicator targetIndicator : targetIndicators) {
                    // 检查门店是否在指定的拜访列表中
                    boolean isValidMerchant = offlineMerchantIds.contains(visitPlan.getMId()) || 
                                            onlineMerchantIds.contains(visitPlan.getMId());
                    
                    if (isValidMerchant) {
                        BdVisitPlanIndicator planIndicator = createPlanIndicator(visitPlan, targetIndicator, syncDataMap);
                        planIndicators.add(planIndicator);
                    }
                }
            }
            
            log.debug("生成拜访计划指标，拜访计划数量: {}, 目标指标数量: {}, 生成指标数量: {}", 
                    visitPlans.size(), targetIndicators.size(), planIndicators.size());
            
        } catch (Exception e) {
            log.error(ERROR_GENERATE_PLAN_INDICATORS_FAILED, e);
        }
        
        return planIndicators;
    }
    
    /**
     * 构建同步数据Map，以门店ID+目标指标ID作为key
     */
    private Map<String, BdVisitPlanIndicatorSync> buildSyncDataMap(List<BdVisitPlanIndicatorSync> indicatorSyncData) {
        Map<String, BdVisitPlanIndicatorSync> syncDataMap = new HashMap<>();
        
        if (!CollectionUtils.isEmpty(indicatorSyncData)) {
            for (BdVisitPlanIndicatorSync sync : indicatorSyncData) {
                if (sync.getMId() != null && sync.getBdVisitTargetIndicatorId() != null) {
                    String key = sync.getMId() + "_" + sync.getBdVisitTargetIndicatorId();
                    syncDataMap.put(key, sync);
                }
            }
        }
        
        log.debug("构建同步数据Map，原始数据量: {}, Map大小: {}", 
                indicatorSyncData != null ? indicatorSyncData.size() : 0, syncDataMap.size());
        
        return syncDataMap;
    }
    
    /**
     * 创建拜访计划指标对象
     */
    private BdVisitPlanIndicator createPlanIndicator(BdVisitPlan visitPlan, BdVisitTargetIndicator targetIndicator, Map<String, BdVisitPlanIndicatorSync> syncDataMap) {
        BdVisitPlanIndicator planIndicator = new BdVisitPlanIndicator();
        
        // 关联信息
        planIndicator.setBdVisitPlanId(visitPlan.getId());
        planIndicator.setBdVisitTargetId(visitPlan.getBdVisitTargetId());
        planIndicator.setBdVisitTargetIndicatorId(targetIndicator.getId());
        planIndicator.setBdId(visitPlan.getBdId());
        planIndicator.setMId(visitPlan.getMId());
        
        // 指标信息
         planIndicator.setIndicatorName(targetIndicator.getIndicatorName());
         
         // 从syncDataMap中获取当前值和潜力值
         java.math.BigDecimal currentValue = java.math.BigDecimal.ZERO;
         java.math.BigDecimal potentialValue = java.math.BigDecimal.ZERO;
         
         if (syncDataMap != null && !syncDataMap.isEmpty()) {
             // 根据门店ID和目标指标ID构建key进行快速查找
             String key = visitPlan.getMId() + "_" + targetIndicator.getId();
             BdVisitPlanIndicatorSync matchedSync = syncDataMap.get(key);
             
             if (matchedSync != null) {
                 currentValue = matchedSync.getIndicatorCurrentValue() != null ? matchedSync.getIndicatorCurrentValue() : java.math.BigDecimal.ZERO;
                 potentialValue = matchedSync.getIndicatorPotentialValue() != null ? matchedSync.getIndicatorPotentialValue() : java.math.BigDecimal.ZERO;
                 log.debug("从同步数据Map中获取指标值，门店ID: {}, 目标指标ID: {}, 当前值: {}, 潜力值: {}", 
                         visitPlan.getMId(), targetIndicator.getId(), currentValue, potentialValue);
             }
         }
         
         planIndicator.setIndicatorCurrentValue(currentValue);
         planIndicator.setIndicatorPotentialValue(potentialValue);
        
        // 审计信息
        planIndicator.setCreateTime(LocalDateTime.now());
        planIndicator.setUpdateTime(LocalDateTime.now());
        
        return planIndicator;
    }
    
    /**
     * 批量插入拜访计划指标
     */
    private int insertPlanIndicatorsBatch(List<BdVisitPlanIndicator> planIndicators) {
        try {
            if (CollectionUtils.isEmpty(planIndicators)) {
                return 0;
            }
            
            // 通过Repository层调用insertBatch方法
             return bdVisitPlanIndicatorRepository.insertBatch(planIndicators);
        } catch (Exception e) {
            log.error(ERROR_INSERT_PLAN_INDICATORS_FAILED + "，指标数量: {}", planIndicators.size(), e);
            throw e;
        }
    }
}