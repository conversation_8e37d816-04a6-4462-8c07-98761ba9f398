package net.summerfarm.crm.service.BDVisit.impl;

import lombok.extern.slf4j.Slf4j;
import net.summerfarm.crm.model.entity.BDVisit.BdVisitTargetIndicatorUpdate;
import net.summerfarm.crm.model.entity.BDVisit.BdVisitTargetIndicatorQuery;
import net.summerfarm.crm.mapper.manage.BDVisit.BdVisitTargetIndicatorMapper;
import net.summerfarm.crm.model.domain.BDVisit.BdVisitTargetIndicator;

import net.summerfarm.crm.service.BDVisit.BdVisitTargetIndicatorService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * 销售拜访目标指标服务实现类
 *
 * <AUTHOR>
 * @date 2025-01-14
 */
@Slf4j
@Service
public class BdVisitTargetIndicatorServiceImpl implements BdVisitTargetIndicatorService {

    @Resource
    private BdVisitTargetIndicatorMapper bdVisitTargetIndicatorMapper;

    @Override
    @Transactional(readOnly = true)
    public BdVisitTargetIndicator selectByPrimaryKey(Long id) {
        if (id == null) {
            return null;
        }
        return bdVisitTargetIndicatorMapper.selectByPrimaryKey(id);
    }



    @Override
    @Transactional
    public int updateByPrimaryKeySelective(BdVisitTargetIndicatorUpdate record) {
        if (record == null || record.getId() == null) {
            log.warn("更新参数无效，record: {}", record);
            return 0;
        }
        
        try {
            int rows = bdVisitTargetIndicatorMapper.updateByPrimaryKeySelective(record);
            log.debug("选择性更新拜访目标指标，ID: {}, 影响行数: {}", record.getId(), rows);
            return rows;
        } catch (Exception e) {
            log.error("选择性更新拜访目标指标失败，record: {}", record, e);
            throw e;
        }
    }



    @Override
    @Transactional
    public boolean updateIndicatorCurrentValueAndStatus(Long indicatorId, BigDecimal currentValue, BigDecimal expectedValue) {
        if (indicatorId == null) {
            log.warn("指标ID不能为空");
            return false;
        }
        
        try {
            // 更新当前值
            BdVisitTargetIndicatorUpdate update = new BdVisitTargetIndicatorUpdate();
            update.setId(indicatorId);
            update.setIndicatorCurrentValue(currentValue);
            
            // 计算并设置完成状态
            String indicatorStatus = calculateIndicatorStatus(currentValue, expectedValue);
            update.setIndicatorStatus(Integer.valueOf(indicatorStatus));
            
            int result = updateByPrimaryKeySelective(update);
            boolean success = result > 0;
            
            log.debug("更新指标当前值和状态{}，ID: {}, 当前值: {}, 期望值: {}, 状态: {}", 
                    success ? "成功" : "失败", indicatorId, currentValue, expectedValue, indicatorStatus);
            
            return success;
        } catch (Exception e) {
            log.error("更新指标当前值和状态失败，ID: {}, 当前值: {}, 期望值: {}", 
                    indicatorId, currentValue, expectedValue, e);
            throw e;
        }
    }

    @Override
    public String calculateIndicatorStatus(BigDecimal currentValue, BigDecimal expectedValue) {
        if (currentValue == null || expectedValue == null) {
            log.debug("计算指标状态：参数为空，返回未完成状态");
            return "0"; // 未完成
        }
        
        // 如果当前值大于等于期望值，则完成
        if (currentValue.compareTo(expectedValue) >= 0) {
            log.debug("计算指标状态：当前值({})>=期望值({})，返回已完成状态", currentValue, expectedValue);
            return "1"; // 已完成
        } else {
            log.debug("计算指标状态：当前值({})<期望值({})，返回未完成状态", currentValue, expectedValue);
            return "0"; // 未完成
        }
    }

    @Override
    @Transactional(readOnly = true)
    public List<BdVisitTargetIndicator> selectByIds(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            log.warn("根据ID列表查询拜访目标指标：ID列表为空");
            return null;
        }
        
        try {
            // 使用查询对象进行批量查询
            BdVisitTargetIndicatorQuery query = new BdVisitTargetIndicatorQuery();
            // 这里需要在Query对象中添加ids字段支持，暂时使用循环查询
            List<BdVisitTargetIndicator> result = new ArrayList<>();
            for (Long id : ids) {
                BdVisitTargetIndicator indicator = bdVisitTargetIndicatorMapper.selectByPrimaryKey(id);
                if (indicator != null) {
                    result.add(indicator);
                }
            }
            
            log.debug("根据ID列表查询拜访目标指标：请求数量：{}，结果数量：{}", 
                    ids.size(), result.size());
            return result;
        } catch (Exception e) {
            log.error("根据ID列表查询拜访目标指标失败：请求数量：{}", ids.size(), e);
            throw e;
        }
    }

}