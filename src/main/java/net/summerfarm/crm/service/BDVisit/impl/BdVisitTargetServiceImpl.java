package net.summerfarm.crm.service.BDVisit.impl;

import lombok.extern.slf4j.Slf4j;
import net.summerfarm.crm.common.base.BaseService;
import net.summerfarm.crm.enums.BdAreaConfigEnum;
import net.summerfarm.crm.enums.BDVisit.BdVisitTargetIndicatorEnum;
import net.summerfarm.crm.mapper.repository.BDVisit.BdVisitTargetIndicatorRepository;
import net.summerfarm.crm.mapper.repository.BDVisit.BdVisitTargetRepository;
import net.summerfarm.crm.model.domain.CrmBdOrg;
import net.summerfarm.crm.model.domain.BDVisit.BdVisitTarget;
import net.summerfarm.crm.model.domain.BDVisit.BdVisitTargetIndicator;
import net.summerfarm.crm.model.input.BDVisit.AddBdVisitTargetIndicatorInput;
import net.summerfarm.crm.model.input.BDVisit.BatchAddBdVisitTargetInput;
import net.summerfarm.crm.service.BdAreaConfigService;
import net.summerfarm.crm.service.BDVisit.BdVisitTargetService;
import net.xianmu.common.exception.BizException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;
import javax.annotation.Resource;

/**
 * 销售拜访目标服务实现类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class BdVisitTargetServiceImpl implements BdVisitTargetService {

    @Resource
    private BdVisitTargetRepository bdVisitTargetRepository;

    @Resource
    private BdVisitTargetIndicatorRepository bdVisitTargetIndicatorRepository;

    @Resource
    private BdAreaConfigService bdAreaConfigService;

    @Resource
    private BaseService baseService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchAddBdVisitTarget(BatchAddBdVisitTargetInput input) {
        // 1. 校验输入参数
        validateBatchAddBdVisitTargetInput(input);

        // 2. 校验用户权限
        CrmBdOrg crmBdOrg = bdAreaConfigService.getTopRankOrg();
        validateBatchAddBdVisitTargetPermission(input, crmBdOrg);

        // 3. 判断要创建的销售拜访目标是否已存在
        validateBdVisitTargetsNotExist(input);

        // 4. 批量创建销售拜访目标
        List<BdVisitTarget> bdVisitTargets = batchCreateBdVisitTargets(input, crmBdOrg);

        // 5. 批量创建销售拜访目标指标
        batchCreateBdVisitTargetIndicators(input, bdVisitTargets);
    }

    private void validateBatchAddBdVisitTargetInput(BatchAddBdVisitTargetInput input) {
        if (CollectionUtils.isEmpty(input.getBdIds())) {
            throw new BizException("销售ID列表不能为空");
        }
        if (input.getTargetBeginDate() == null || input.getTargetEndDate() == null) {
            throw new BizException("目标开始日期和目标结束日期不能为空");
        }
        if (input.getTargetBeginDate().isAfter(input.getTargetEndDate())) {
            throw new BizException("目标开始日期不能晚于目标结束日期");
        }
        if (input.getTargetBeginDate().isBefore(LocalDate.now())) {
            throw new BizException("目标开始日期不能早于当前日期");
        }
        if (CollectionUtils.isEmpty(input.getAddBdVisitTargetIndicators())) {
            throw new BizException("销售拜访目标指标列表不能为空");
        }
        // 校验销售拜访目标指标列表中优先级不能重复
        Set<Integer> uniquePriorities = input.getAddBdVisitTargetIndicators().stream()
                .map(AddBdVisitTargetIndicatorInput::getPriority)
                .collect(Collectors.toSet());
        if (uniquePriorities.size() != input.getAddBdVisitTargetIndicators().size()) {
            throw new BizException("销售拜访目标指标的优先级不能重复");
        }
    }

    private void validateBatchAddBdVisitTargetPermission(BatchAddBdVisitTargetInput input, CrmBdOrg crmBdOrg) {
        if (crmBdOrg == null) {
            throw new BizException("未找到您的销售身份信息，请联系销售主管配置");
        }
        if (crmBdOrg.getRank() != BdAreaConfigEnum.SaleRank.CITY_MANAGER) {
            throw new BizException("只有销售主管才能创建销售拜访目标");
        }
        // 获取当前登录主管下属的所有销售ID
        List<Integer> bdIds = bdAreaConfigService.listChildrenBd().stream()
                .map(CrmBdOrg::getBdId)
                .collect(Collectors.toList());
        List<Integer> invalidBdIds = input.getBdIds().stream().filter(bdId -> !bdIds.contains(bdId)).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(invalidBdIds)) {
            throw new BizException("以下销售id不存在或不是你的下属销售："
                    + invalidBdIds.stream().map(String::valueOf).collect(Collectors.joining(",")));
        }
    }

    /**
     * 验证销售拜访目标不存在
     */
    private void validateBdVisitTargetsNotExist(BatchAddBdVisitTargetInput input) {
        // 根据日期范围查询已存在的销售拜访目标
        List<BdVisitTarget> existingTargets = bdVisitTargetRepository.selectByBdIdsAndDateRange(input.getBdIds(),
                input.getTargetBeginDate(), input.getTargetEndDate());
        if (CollectionUtils.isEmpty(existingTargets)) {
            return;
        }
        Set<String> existingBdNames = existingTargets.stream().map(BdVisitTarget::getBdName).collect(Collectors.toSet());
        throw new BizException("以下销售在" + input.getTargetBeginDate() + "至" + input.getTargetEndDate() + "期间已存在拜访目标："
                + String.join(", ", existingBdNames));
    }

    /**
     * 批量创建销售拜访目标
     * 
     * @param crmBdOrg
     */
    private List<BdVisitTarget> batchCreateBdVisitTargets(BatchAddBdVisitTargetInput input, CrmBdOrg crmBdOrg) {
        Map<Integer, String> bdIdNameMap = baseService.getBdRealNames(input.getBdIds());

        List<BdVisitTarget> bdVisitTargets = new ArrayList<>();
        for (Integer bdId : input.getBdIds()) {
            for (LocalDate targetDate = input.getTargetBeginDate(); targetDate
                    .compareTo(input.getTargetEndDate()) <= 0; targetDate = targetDate.plusDays(1L)) {
                BdVisitTarget target = new BdVisitTarget();
                target.setBdId(bdId);
                target.setBdName(bdIdNameMap.get(bdId));
                target.setTargetDate(targetDate);
                target.setTargetCreator(crmBdOrg.getBdId());
                target.setTargetCreatorName(crmBdOrg.getBdName());
                bdVisitTargets.add(target);
            }
        }

        bdVisitTargetRepository.batchInsert(bdVisitTargets);

        return bdVisitTargets;
    }

    /**
     * 批量创建销售拜访目标指标
     */
    private void batchCreateBdVisitTargetIndicators(BatchAddBdVisitTargetInput input, List<BdVisitTarget> bdVisitTargets) {
        List<BdVisitTargetIndicator> indicators = new ArrayList<>();
        for (BdVisitTarget bdVisitTarget : bdVisitTargets) {
            for (AddBdVisitTargetIndicatorInput indicatorInput : input.getAddBdVisitTargetIndicators()) {
                BdVisitTargetIndicator indicator = new BdVisitTargetIndicator();
                indicator.setCityManagerVisitTargetId(bdVisitTarget.getCityManagerVisitTargetId());
                indicator.setBdVisitTargetId(bdVisitTarget.getId());
                indicator.setBdId(bdVisitTarget.getBdId());
                indicator.setBdName(bdVisitTarget.getBdName());
                indicator.setPriority(indicatorInput.getPriority());
                indicator.setIndicatorType(indicatorInput.getIndicatorType());
                indicator.setIndicatorName(indicatorInput.getIndicatorName());
                indicator.setTargetDate(bdVisitTarget.getTargetDate());
                indicator.setBusinessType(indicatorInput.getBusinessType());
                indicator.setMetricsMethod(indicatorInput.getMetricsMethod());
                indicator.setCategoryName(indicatorInput.getCategoryName());
                indicator.setSku(indicatorInput.getSku());
                indicator.setIndicatorExpectedValue(indicatorInput.getIndicatorExpectedValue());
                indicator.setIndicatorStatus(BdVisitTargetIndicatorEnum.IndicatorStatus.INCOMPLETE.getCode());
                indicators.add(indicator);
            }
        }

        bdVisitTargetIndicatorRepository.batchInsert(indicators);
    }

}
