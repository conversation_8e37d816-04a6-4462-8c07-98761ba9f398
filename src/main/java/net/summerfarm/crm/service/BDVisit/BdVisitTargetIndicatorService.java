package net.summerfarm.crm.service.BDVisit;

import net.summerfarm.crm.model.entity.BDVisit.BdVisitTargetIndicatorUpdate;
import net.summerfarm.crm.model.domain.BDVisit.BdVisitTargetIndicator;
import java.math.BigDecimal;
import java.util.List;

/**
 * 销售拜访目标指标服务接口
 *
 * <AUTHOR>
 * @date 2025-01-14
 */
public interface BdVisitTargetIndicatorService {

    /**
     * 根据主键查询销售拜访目标指标
     *
     * @param id 主键ID
     * @return 销售拜访目标指标
     */
    BdVisitTargetIndicator selectByPrimaryKey(Long id);

    /**
     * 选择性更新销售拜访目标指标
     *
     * @param record 更新记录
     * @return 更新行数
     */
    int updateByPrimaryKeySelective(BdVisitTargetIndicatorUpdate record);

    /**
     * 更新指标当前值和完成状态
     *
     * @param indicatorId 指标ID
     * @param currentValue 当前值
     * @param expectedValue 期望值
     * @return 是否更新成功
     */
    boolean updateIndicatorCurrentValueAndStatus(Long indicatorId, BigDecimal currentValue, BigDecimal expectedValue);

    /**
     * 计算指标完成状态
     * @param currentValue 当前值
     * @param expectedValue 期望值
     * @return 完成状态字符串
     */
    String calculateIndicatorStatus(BigDecimal currentValue, BigDecimal expectedValue);

    /**
     * 根据ID列表批量查询销售拜访目标指标
     *
     * @param ids ID列表
     * @return 销售拜访目标指标列表
     */
    List<BdVisitTargetIndicator> selectByIds(List<Long> ids);

}