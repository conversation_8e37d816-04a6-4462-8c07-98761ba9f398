package net.summerfarm.crm.service.BDVisit;

import net.summerfarm.crm.model.domain.BDVisit.BdVisitPlanIndicatorSync;

import java.util.List;

/**
 * 销售拜访计划指标同步服务接口
 *
 * <AUTHOR>
 * @date 2025-09-08
 */
public interface BdVisitPlanIndicatorSyncService {



    /**
     * 根据门店ID列表查询同步数据
     *
     * @param merchantIds 门店ID列表
     * @return 同步数据列表
     */
    List<BdVisitPlanIndicatorSync> selectByMerchantIds(List<Long> merchantIds);

    /**
     * 分页查询去重门店ID列表
     *
     * @param offset 偏移量
     * @param limit 限制数量
     * @return 去重门店ID列表
     */
    List<Long> selectDistinctMerchantIdsByPage(int offset, int limit);

    /**
     * 批量处理同步数据（业务层核心方法）
     * 包含：分页查询、数据验证、指标更新、潜力值计算、ES更新等完整流程
     *
     * @param pageSize 分页大小
     */
    void processSyncDataInBatch(int pageSize);
}