package net.summerfarm.crm.service.BDVisit.impl;

import lombok.extern.slf4j.Slf4j;
import net.summerfarm.crm.mapper.repository.BDVisit.BdVisitPlanIndicatorRepository;
import net.summerfarm.crm.mapper.repository.FollowUpRecordRepository;
import net.summerfarm.crm.model.domain.BDVisit.BdVisitPlanIndicator;
import net.summerfarm.crm.model.domain.BDVisit.BdVisitPlanIndicatorSync;
import net.summerfarm.crm.model.domain.BDVisit.BdVisitTargetIndicator;
import net.summerfarm.crm.service.BDVisit.BdVisitTargetIndicatorService;
import net.summerfarm.crm.service.BDVisit.MerchantPotentialValueCalculationService;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import net.summerfarm.crm.model.bo.BDVisit.MerchantPotentialCalculationData;


import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 门店潜力值计算服务实现类
 *
 * <AUTHOR>
 * @date 2025-09-08
 */
@Slf4j
@Service
public class MerchantPotentialValueCalculationServiceImpl implements MerchantPotentialValueCalculationService {

    @Resource
    private BdVisitPlanIndicatorRepository bdVisitPlanIndicatorRepository;
    
    @Resource
    private BdVisitTargetIndicatorService bdVisitTargetIndicatorService;
    
    @Resource
    private FollowUpRecordRepository followUpRecordRepository;

    // 权重分配常量
    private static final BigDecimal HIGH_PRIORITY_WEIGHT_RATIO = new BigDecimal("0.8"); // 前三优先级占80%
    private static final BigDecimal LOW_PRIORITY_WEIGHT_RATIO = new BigDecimal("0.2");  // 其他优先级占20%
    private static final BigDecimal VISITED_COEFFICIENT = new BigDecimal("0.2");        // 近7天拜访过的系数
    private static final BigDecimal NOT_VISITED_COEFFICIENT = BigDecimal.ONE;           // 未拜访的系数
    private static final int SCALE = 4; // 计算精度

    @Override
    public BigDecimal calculateComprehensivePotentialValue(Long merchantId, 
                                                           List<BdVisitPlanIndicator> planIndicators,
                                                           List<BdVisitTargetIndicator> targetIndicators) {
        if (merchantId == null || CollectionUtils.isEmpty(planIndicators) || CollectionUtils.isEmpty(targetIndicators)) {
            log.warn("计算门店综合潜力值参数无效，merchantId: {}, planIndicators: {}, targetIndicators: {}", 
                    merchantId, planIndicators != null ? planIndicators.size() : 0, 
                    targetIndicators != null ? targetIndicators.size() : 0);
            return BigDecimal.ZERO;
        }

        try {
            // 1. 计算目标权重
            Map<Long, BigDecimal> targetWeights = calculateTargetWeights(targetIndicators);
            
            // 2. 计算基础潜力值：V = (Score_目标1 * W_目标1) + (Score_目标2 * W_目标2) + ...
            BigDecimal basePotentialValue = BigDecimal.ZERO;
            
            for (BdVisitPlanIndicator planIndicator : planIndicators) {
                Long targetIndicatorId = planIndicator.getBdVisitTargetIndicatorId();
                BigDecimal weight = targetWeights.get(targetIndicatorId);
                BigDecimal potentialValue = planIndicator.getIndicatorPotentialValue();
                
                if (weight != null && potentialValue != null) {
                    BigDecimal weightedValue = potentialValue.multiply(weight);
                    basePotentialValue = basePotentialValue.add(weightedValue);
                }
            }
            
            // 3. 获取拜访系数
            BigDecimal visitCoefficient = getMerchantVisitCoefficient(merchantId);
            
            // 4. 计算最终潜力值：V_final = V * Penalty_Factor
            BigDecimal finalPotentialValue = basePotentialValue.multiply(visitCoefficient)
                    .setScale(SCALE, RoundingMode.HALF_UP);
            
            log.debug("门店{}潜力值计算完成：基础潜力值={}, 拜访系数={}, 最终潜力值={}", 
                    merchantId, basePotentialValue, visitCoefficient, finalPotentialValue);
            
            return finalPotentialValue;
            
        } catch (Exception e) {
            log.error("计算门店{}综合潜力值失败", merchantId, e);
            return BigDecimal.ZERO;
        }
    }

    @Override
    public Map<Long, BigDecimal> batchCalculateComprehensivePotentialValue(List<Long> merchantIds) {
        if (CollectionUtils.isEmpty(merchantIds)) {
            log.warn("批量计算门店潜力值：门店ID列表为空");
            return new HashMap<>();
        }

        Map<Long, BigDecimal> result = new HashMap<>();
        
        try {
            // 批量查询拜访计划指标
            List<BdVisitPlanIndicator> allPlanIndicators = bdVisitPlanIndicatorRepository.selectByMerchantIds(merchantIds);
            if (CollectionUtils.isEmpty(allPlanIndicators)) {
                log.warn("批量计算门店潜力值：未找到拜访计划指标数据");
                return result;
            }
            
            // 按门店ID分组
            Map<Long, List<BdVisitPlanIndicator>> planIndicatorsByMerchant = allPlanIndicators.stream()
                    .collect(Collectors.groupingBy(BdVisitPlanIndicator::getMId));
            
            // 获取所有目标指标ID
            Set<Long> targetIndicatorIds = allPlanIndicators.stream()
                    .map(BdVisitPlanIndicator::getBdVisitTargetIndicatorId)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toSet());
            
            // 批量查询目标指标（使用selectByTargetIndicatorIds）
            List<BdVisitPlanIndicator> targetPlanIndicators = bdVisitPlanIndicatorRepository.selectByTargetIndicatorIds(new ArrayList<>(targetIndicatorIds));
            if (CollectionUtils.isEmpty(targetPlanIndicators)) {
                log.warn("批量计算门店潜力值：未找到目标计划指标数据");
                return result;
            }
            
            // 批量查询目标指标
            List<BdVisitTargetIndicator> allTargetIndicators = bdVisitTargetIndicatorService.selectByIds(new ArrayList<>(targetIndicatorIds));
            if (CollectionUtils.isEmpty(allTargetIndicators)) {
                log.warn("批量计算门店潜力值：未找到目标指标数据");
                return result;
            }
            
            // 将目标指标按ID分组，便于快速查找
            Map<Long, BdVisitTargetIndicator> targetIndicatorMap = allTargetIndicators.stream()
                    .collect(Collectors.toMap(BdVisitTargetIndicator::getId, indicator -> indicator));
            
            // 批量获取拜访系数
            Map<Long, BigDecimal> visitCoefficients = batchGetMerchantVisitCoefficient(merchantIds);
            
            // 为每个门店计算潜力值
            for (Long merchantId : merchantIds) {
                List<BdVisitPlanIndicator> planIndicators = planIndicatorsByMerchant.get(merchantId);
                if (!CollectionUtils.isEmpty(planIndicators)) {
                    // 获取该门店对应的目标指标
                    List<BdVisitTargetIndicator> merchantTargetIndicators = planIndicators.stream()
                            .map(BdVisitPlanIndicator::getBdVisitTargetIndicatorId)
                            .filter(Objects::nonNull)
                            .map(targetIndicatorMap::get)
                            .filter(Objects::nonNull)
                            .collect(Collectors.toList());
                    
                    if (!CollectionUtils.isEmpty(merchantTargetIndicators)) {
                        BigDecimal potentialValue = calculateComprehensivePotentialValue(merchantId, planIndicators, merchantTargetIndicators);
                        result.put(merchantId, potentialValue);
                    } else {
                        log.warn("门店{}未找到对应的目标指标，跳过计算", merchantId);
                        result.put(merchantId, BigDecimal.ZERO);
                    }
                } else {
                    log.warn("门店{}未找到拜访计划指标，跳过计算", merchantId);
                    result.put(merchantId, BigDecimal.ZERO);
                }
            }
            
            log.info("批量计算门店潜力值完成，处理门店数量：{}", result.size());
            
        } catch (Exception e) {
            log.error("批量计算门店潜力值失败", e);
        }
        
        return result;
    }

    @Override
    public Map<Long, BigDecimal> batchCalculateComprehensivePotentialValueFromSyncData(List<BdVisitPlanIndicatorSync> syncDataList) {
        if (CollectionUtils.isEmpty(syncDataList)) {
            log.warn("批量计算门店潜力值（同步数据）：同步数据列表为空");
            return new HashMap<>();
        }

        Map<Long, BigDecimal> result = new HashMap<>();
        
        try {
            // 从同步数据中提取门店ID和目标指标ID
            Set<Long> merchantIds = syncDataList.stream()
                    .map(BdVisitPlanIndicatorSync::getMId)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toSet());
            
            Set<Long> targetIndicatorIds = syncDataList.stream()
                    .map(BdVisitPlanIndicatorSync::getBdVisitTargetIndicatorId)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toSet());
            
            // 批量查询目标指标
            List<BdVisitTargetIndicator> allTargetIndicators = bdVisitTargetIndicatorService.selectByIds(new ArrayList<>(targetIndicatorIds));
            if (CollectionUtils.isEmpty(allTargetIndicators)) {
                log.warn("批量计算门店潜力值（同步数据）：未找到目标指标数据");
                return result;
            }
            
            // 将目标指标按ID分组，便于快速查找
            Map<Long, BdVisitTargetIndicator> targetIndicatorMap = allTargetIndicators.stream()
                    .collect(Collectors.toMap(BdVisitTargetIndicator::getId, indicator -> indicator));
            
            // 从同步数据组装生成MerchantPotentialCalculationData对象列表
            List<MerchantPotentialCalculationData> calculationDataList = syncDataList.stream()
                    .map(syncData -> convertSyncDataToCalculationData(syncData, targetIndicatorMap))
                    .collect(Collectors.toList());
            
            // 计算目标权重
            Map<Long, BigDecimal> targetWeights = calculateTargetWeights(allTargetIndicators);
            
            // 为每个计算数据设置权重
            calculationDataList.forEach(data -> {
                BigDecimal weight = targetWeights.get(data.getBdVisitTargetIndicatorId());
                data.setTargetIndicatorWeight(weight != null ? weight : BigDecimal.ZERO);
            });
            
            // 按门店ID分组
            Map<Long, List<MerchantPotentialCalculationData>> calculationDataByMerchant = calculationDataList.stream()
                    .collect(Collectors.groupingBy(MerchantPotentialCalculationData::getMerchantId));
            
            // 批量获取拜访系数
            Map<Long, BigDecimal> visitCoefficients = batchGetMerchantVisitCoefficient(new ArrayList<>(merchantIds));
            
            // 为每个门店计算潜力值
            for (Long merchantId : merchantIds) {
                List<MerchantPotentialCalculationData> calculationData = calculationDataByMerchant.get(merchantId);
                if (!CollectionUtils.isEmpty(calculationData)) {
                    BigDecimal potentialValue = calculateComprehensivePotentialValueFromCalculationData(merchantId, calculationData, visitCoefficients);
                    result.put(merchantId, potentialValue);
                } else {
                    log.warn("门店{}未找到拜访计划指标，跳过计算", merchantId);
                    result.put(merchantId, BigDecimal.ZERO);
                }
            }
            
            log.info("批量计算门店潜力值（同步数据）完成，处理门店数量：{}", result.size());
            
        } catch (Exception e) {
            log.error("批量计算门店潜力值（同步数据）失败", e);
        }
        
        return result;
    }

    @Override
    public Map<Long, BigDecimal> calculateTargetWeights(List<BdVisitTargetIndicator> targetIndicators) {
        if (CollectionUtils.isEmpty(targetIndicators)) {
            log.warn("计算目标权重：目标指标列表为空");
            return new HashMap<>();
        }

        Map<Long, BigDecimal> weights = new HashMap<>();
        
        try {
            // 按优先级排序（假设优先级数字越小越重要）
            List<BdVisitTargetIndicator> sortedIndicators = targetIndicators.stream()
                    .sorted(Comparator.comparing(BdVisitTargetIndicator::getPriority, 
                            Comparator.nullsLast(Comparator.naturalOrder())))
                    .collect(Collectors.toList());
            
            int totalCount = sortedIndicators.size();
            int highPriorityCount = Math.min(3, totalCount); // 前三个优先级
            int lowPriorityCount = totalCount - highPriorityCount;
            
            // 计算高优先级权重（前三个平分80%）
            BigDecimal highPriorityWeight = BigDecimal.ZERO;
            if (highPriorityCount > 0) {
                highPriorityWeight = HIGH_PRIORITY_WEIGHT_RATIO
                        .divide(BigDecimal.valueOf(highPriorityCount), SCALE, RoundingMode.HALF_UP);
            }
            
            // 计算低优先级权重（其余平分20%）
            BigDecimal lowPriorityWeight = BigDecimal.ZERO;
            if (lowPriorityCount > 0) {
                lowPriorityWeight = LOW_PRIORITY_WEIGHT_RATIO
                        .divide(BigDecimal.valueOf(lowPriorityCount), SCALE, RoundingMode.HALF_UP);
            }
            
            // 分配权重
            for (int i = 0; i < sortedIndicators.size(); i++) {
                BdVisitTargetIndicator indicator = sortedIndicators.get(i);
                BigDecimal weight = (i < highPriorityCount) ? highPriorityWeight : lowPriorityWeight;
                weights.put(indicator.getId(), weight);
            }
            
            log.debug("目标权重计算完成：总指标数={}, 高优先级数={}, 低优先级数={}, 高优先级权重={}, 低优先级权重={}", 
                    totalCount, highPriorityCount, lowPriorityCount, highPriorityWeight, lowPriorityWeight);
            
        } catch (Exception e) {
            log.error("计算目标权重失败", e);
        }
        
        return weights;
    }

    @Override
    public BigDecimal getMerchantVisitCoefficient(Long merchantId) {
        if (merchantId == null) {
            log.warn("获取门店拜访系数：门店ID为空");
            return NOT_VISITED_COEFFICIENT;
        }

        try {
            // 检查近7天是否有拜访记录
            LocalDateTime sevenDaysAgo = LocalDateTime.now().minusDays(7);
            
            // 这里需要实现查询近7天拜访记录的逻辑
            // boolean hasRecentVisit = visitRecordService.hasVisitInPeriod(merchantId, sevenDaysAgo, LocalDateTime.now());
            boolean hasRecentVisit = false; // 临时设置
            
            BigDecimal coefficient = hasRecentVisit ? VISITED_COEFFICIENT : NOT_VISITED_COEFFICIENT;
            
            log.debug("门店{}拜访系数：近7天拜访={}, 系数={}", merchantId, hasRecentVisit, coefficient);
            
            return coefficient;
            
        } catch (Exception e) {
            log.error("获取门店{}拜访系数失败", merchantId, e);
            return NOT_VISITED_COEFFICIENT;
        }
    }

    @Override
    public Map<Long, BigDecimal> batchGetMerchantVisitCoefficient(List<Long> merchantIds) {
        if (CollectionUtils.isEmpty(merchantIds)) {
            log.warn("批量获取门店拜访系数：门店ID列表为空");
            return new HashMap<>();
        }

        Map<Long, BigDecimal> coefficients = new HashMap<>();
        
        try {
            // 批量查询近7天拜访记录
            LocalDateTime sevenDaysAgo = LocalDateTime.now().minusDays(7);
            
            // 通过repository查询近7天有拜访记录的门店ID
            List<Long> visitedMerchantIdList = followUpRecordRepository.getVisitedMerchantIds(merchantIds, sevenDaysAgo, LocalDateTime.now());
            Set<Long> visitedMerchantIds = new HashSet<>(visitedMerchantIdList);
            
            // 为每个门店分配系数
            for (Long merchantId : merchantIds) {
                BigDecimal coefficient = visitedMerchantIds.contains(merchantId) ? 
                        VISITED_COEFFICIENT : NOT_VISITED_COEFFICIENT;
                coefficients.put(merchantId, coefficient);
            }
            
            log.debug("批量获取门店拜访系数完成：总门店数={}, 近7天拜访门店数={}", 
                    merchantIds.size(), visitedMerchantIds.size());
            
        } catch (Exception e) {
            log.error("批量获取门店拜访系数失败", e);
            
            // 异常情况下，所有门店都设置为未拜访系数
            for (Long merchantId : merchantIds) {
                coefficients.put(merchantId, NOT_VISITED_COEFFICIENT);
            }
        }
        
        return coefficients;
    }

    /**
     * 基于计算数据计算门店综合潜力值
     * 直接使用MerchantPotentialCalculationData中的权重和潜力值数据
     *
     * @param merchantId 门店ID
     * @param calculationDataList 计算数据列表
     * @return 综合潜力值
     */
    private BigDecimal calculateComprehensivePotentialValueFromCalculationData(Long merchantId, 
                                                                              List<MerchantPotentialCalculationData> calculationDataList,
                                                                              Map<Long, BigDecimal> visitCoefficientsMap) {
        if (merchantId == null || CollectionUtils.isEmpty(calculationDataList)) {
            log.warn("计算门店综合潜力值参数无效，merchantId: {}, calculationDataList: {}", 
                    merchantId, calculationDataList != null ? calculationDataList.size() : 0);
            return BigDecimal.ZERO;
        }

        try {
            // 计算基础潜力值：V = (Score_目标1 * W_目标1) + (Score_目标2 * W_目标2) + ...
            BigDecimal basePotentialValue = BigDecimal.ZERO;
            
            for (MerchantPotentialCalculationData data : calculationDataList) {
                BigDecimal weight = data.getTargetIndicatorWeight();
                BigDecimal potentialValue = data.getIndicatorPotentialValue();
                
                if (weight != null && potentialValue != null) {
                    BigDecimal weightedValue = potentialValue.multiply(weight);
                    basePotentialValue = basePotentialValue.add(weightedValue);
                }
            }
            
            // 获取拜访系数
            BigDecimal visitCoefficient = visitCoefficientsMap.getOrDefault(merchantId, NOT_VISITED_COEFFICIENT);
            
            // 计算最终潜力值：V_final = V * Penalty_Factor
            BigDecimal finalPotentialValue = basePotentialValue.multiply(visitCoefficient)
                    .setScale(SCALE, RoundingMode.HALF_UP);
            
            log.debug("门店{}潜力值计算完成：基础潜力值={}, 拜访系数={}, 最终潜力值={}", 
                    merchantId, basePotentialValue, visitCoefficient, finalPotentialValue);
            
            return finalPotentialValue;
            
        } catch (Exception e) {
            log.error("计算门店{}综合潜力值失败", merchantId, e);
            return BigDecimal.ZERO;
        }
    }

    /**
     * 将BdVisitPlanIndicatorSync转换为MerchantPotentialCalculationData
     */
    private MerchantPotentialCalculationData convertSyncDataToCalculationData(BdVisitPlanIndicatorSync syncData, 
                                                                                Map<Long, BdVisitTargetIndicator> targetIndicatorMap) {
        MerchantPotentialCalculationData data = new MerchantPotentialCalculationData();
        data.setMerchantId(syncData.getMId());
        data.setBdVisitPlanId(syncData.getBdVisitPlanId());
        data.setBdVisitTargetIndicatorId(syncData.getBdVisitTargetIndicatorId());
        data.setIndicatorCurrentValue(syncData.getIndicatorCurrentValue());
        data.setIndicatorPotentialValue(syncData.getIndicatorPotentialValue());
        
        // 从目标指标映射中获取优先级信息并设置到计算数据中
        BdVisitTargetIndicator targetIndicator = targetIndicatorMap.get(syncData.getBdVisitTargetIndicatorId());
        if (targetIndicator != null) {
            data.setTargetIndicatorName(targetIndicator.getIndicatorName());
            data.setTargetIndicatorType(targetIndicator.getIndicatorType());
            data.setPriority(targetIndicator.getPriority());
            // 注意：这里暂时不设置权重，权重需要通过calculateTargetWeights方法计算
            // 权重计算需要所有目标指标的优先级信息，在调用方处理
        }
        
        return data;
    }

    /**
     * 将MerchantPotentialCalculationData转换为BdVisitPlanIndicator
     */
    private BdVisitPlanIndicator convertCalculationDataToPlanIndicator(MerchantPotentialCalculationData calculationData) {
        BdVisitPlanIndicator planIndicator = new BdVisitPlanIndicator();
        planIndicator.setMId(calculationData.getMerchantId());
        planIndicator.setBdVisitPlanId(calculationData.getBdVisitPlanId());
        planIndicator.setBdVisitTargetIndicatorId(calculationData.getBdVisitTargetIndicatorId());
        planIndicator.setIndicatorCurrentValue(calculationData.getIndicatorCurrentValue());
        planIndicator.setIndicatorPotentialValue(calculationData.getIndicatorPotentialValue());
        return planIndicator;
    }
}