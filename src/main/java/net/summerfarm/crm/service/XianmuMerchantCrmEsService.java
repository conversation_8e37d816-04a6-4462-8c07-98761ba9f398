package net.summerfarm.crm.service;

import com.github.pagehelper.PageInfo;
import net.summerfarm.crm.model.query.objectiveManagement.NearbyPoiPotentialMerchantQuery;
import net.summerfarm.crm.model.query.objectiveManagement.RecommendPotentialMerchantQuery;
import net.summerfarm.crm.model.vo.objectiveManagement.MerchantVisitPotentialVO;

/**
 * 鲜沐商户CRM ES服务接口
 * 
 * <AUTHOR>
 * @date 2024-01-09
 */
public interface XianmuMerchantCrmEsService {

    /**
     * 查询附近POI潜力值客户
     * 
     * @param query 查询条件
     * @return 分页结果
     */
    PageInfo<MerchantVisitPotentialVO> queryNearbyPoiPotentialMerchants(NearbyPoiPotentialMerchantQuery query);
    
    /**
     * 查询推荐潜力值客户
     * 
     * @param query 查询条件
     * @return 分页结果
     */
    PageInfo<MerchantVisitPotentialVO> queryRecommendPotentialMerchants(RecommendPotentialMerchantQuery query);
}