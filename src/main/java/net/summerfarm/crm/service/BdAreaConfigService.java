package net.summerfarm.crm.service;

import com.github.pagehelper.PageInfo;
import net.summerfarm.crm.model.domain.CrmBdOrg;
import net.summerfarm.crm.model.domain.CrmSalesArea;
import net.summerfarm.crm.model.domain.CrmSalesCity;
import net.summerfarm.crm.model.dto.areaConfig.BdOrgChangeAreaM1DTO;
import net.summerfarm.crm.model.dto.areaConfig.BdOrgUpsertDTO;
import net.summerfarm.crm.model.dto.areaConfig.BdSaleAreaUpsertDTO;
import net.summerfarm.crm.model.vo.areaConfig.*;
import net.summerfarm.crm.model.query.areaConfig.BdAreaConfigQuery;
import net.xianmu.common.result.CommonResult;

import java.util.List;

/**
 * 销售区域配置
 *
 * <AUTHOR>
 * @date 2023/8/24 18:28
 */
public interface BdAreaConfigService {
    /**
     * 销售区域配置列表
     *
     * @param qry qry
     * @return {@link PageInfo}<{@link BdAreaConfigVo}>
     */
    PageInfo<BdAreaConfigVo> listBdAreaConfig(BdAreaConfigQuery qry);

    /**
     * 区域配置详细信息
     *
     * @param cityAdminId m1 id
     * @param salesAreaId 销售区域 id
     * @return {@link BdAreaConfigDetail}
     */
    BdAreaConfigDetail areaConfigDetail(Integer cityAdminId, Integer salesAreaId);


    /**
     * 销售组织配置
     *
     * @param upsertDTO upsert dto
     * @return {@link CommonResult}<{@link Void}>
     */
    CommonResult<Integer> bdOrgUpsert(BdOrgUpsertDTO upsertDTO);

    /**
     * 变更城市主管(m1)
     */
    void changeCityManager(BdOrgChangeAreaM1DTO dto);

    /**
     * 查询指定级别的 bd
     *
     * @param rank 排名
     * @return {@link List}<{@link CrmBdOrg}>
     */
    List<CrmBdOrg> listBdOrgByRank(List<Integer> rank);
    /**
     * 区域配置新增
     *
     * @param upsertDTO upsert dto
     * @return {@link CommonResult}<{@link Void}>
     */
    CommonResult<Void> areaConfigSave( BdSaleAreaUpsertDTO upsertDTO);

    /**
     * 区域配置更新
     *
     * @param upsertDTO upsert dto
     * @return {@link CommonResult}<{@link Void}>
     */
    CommonResult<Void> areaConfigUpdate(BdSaleAreaUpsertDTO upsertDTO);

    /**
     * 销售区域列表
     *
     * @return {@link CommonResult}<{@link List}<{@link SalesAreaVo}>>
     */
    List<SalesAreaVo> listSalesArea(Integer orgId);

    /**
     * 获取当前用户下属的销售区域列表
     *
     * @return
     */
    List<SalesAreaBaseVo> listSalesArea();

    /**
     * 删除区域配置
     *
     * @param salesAreaId 销售区域id
     * @param cityOrgId   城市负责人 id
     * @return {@link CommonResult}<{@link Void}>
     */
    CommonResult<Void> deleteAreaConfig(Integer cityOrgId,Integer salesAreaId);

    /**
     * 获取当前用户的所有bd
     *
     * @return {@link List}<{@link CrmBdOrgVo}>
     */
    List<CrmBdOrg>  listChildrenBd();

    /**
     * 获取当前用户的所有bd
     *
     * @param bdOrgId
     * @return {@link List}<{@link CrmBdOrgVo}>
     */
    List<CrmBdOrg>  listChildrenBd(Integer bdOrgId);

    /**
     * 获取当前用户的所有上级
     *
     * @return {@link List}<{@link CrmBdOrgVo}>
     */
    List<CrmBdOrg>  listParentOrg();

    /**
     * 获取当前用户的所有上级
     *
     * @return {@link List}<{@link CrmBdOrgVo}>
     */
    List<CrmBdOrg>  listParentOrg(Integer bdId);



    /**
     * 获取当前用户的所有上级
     *
     * @param adminId 管理员id
     * @param notContainChildren 是否包含自身，true 不包括，false 包括
     * @return {@link List}<{@link CrmBdOrgVo}>
     */
    List<CrmBdOrg>  listParentOrg(Integer adminId,Boolean notContainChildren);

    /**
     * 通过城市查看城市负责人和汇报人
     *
     * @param city               城市
     * @param province           省
     * @param area               区域
     * @param notContainChildren 是否包含自身，true 不包括，false 包括
     * @return {@link List}<{@link CrmBdOrg}>
     */
    List<CrmBdOrg> selectOrgByCity(String province, String city, String area, Boolean notContainChildren);

    /**
     * 查询所有下属
     *
     * @param parentId 父id
     * @return {@link List}<{@link CrmBdOrg}>
     */
    void listChildrenByParentId(Integer parentId,List<CrmBdOrg> orgList);

    /**
     * 查询所有上级
     *
     * @param parentId 父id
     * @return {@link List}<{@link CrmBdOrg}>
     */
    void listParentByByParentId(Integer parentId,List<CrmBdOrg> orgList);

    /**
     * 查询所有上级
     *
     * @param orgId 销售 orgId
     * @param notContainChildren 是否包含自身，true 不包括，false 包括
     * @return {@link List}<{@link CrmBdOrg}>
     */
    List<CrmBdOrg> listParentByByParentId(Integer orgId,Boolean notContainChildren);

    /**
     * 查询所有上级
     *
     * @param adminId 销售 adminid
     * @param rank 等级
     * @param notContainChildren 是否包含自身，true 不包括，false 包括
     * @return {@link List}<{@link CrmBdOrg}>
     */
    List<CrmBdOrg> listParentByBdAdminId(Integer adminId,Integer rank,Boolean notContainChildren);

    /**
     * 查询所有的销售城市
     *
     * @return {@link List}<{@link CrmSalesCity}>
     */
    List<CrmSalesCity> selectSalesCity();

    /**
     * 获取当前用户最高职级
     *
     * @return {@link CrmBdOrg}
     */
    CrmBdOrg getTopRankOrg();

    /**
     * 获取属于该销售区域的所有bd
     */
    List<CrmBdOrg> listBdOrgBySalesArea(Integer salesAreaId);

    /**
     * 为m1及以上级别的销售主管列出管辖的所有销售区域
     */
    List<CrmSalesArea> listSalesAreasManagedByManager(CrmBdOrg managerOrg);
}
