package net.summerfarm.crm.controller.objectiveManagement;

import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.crm.model.input.BDVisit.*;
import net.summerfarm.crm.model.query.objectiveManagement.*;
import net.summerfarm.crm.model.vo.objectiveManagement.BdVisitPlanVO;
import net.summerfarm.crm.model.vo.objectiveManagement.BdVisitTargetIndicatorVO;
import net.summerfarm.crm.model.vo.objectiveManagement.BdVisitTargetVO;
import net.summerfarm.crm.model.vo.objectiveManagement.MerchantVisitPotentialVO;
import net.xianmu.common.result.CommonResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 目标管理
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping(value = "/crm-service/objective-management")
public class ObjectiveManagementController {

    /**
     * 查询指定位置的潜力值私海客户组
     *
     * @param queryVO 查询参数
     * @return 私海客户列表
     */
    @PostMapping("/query/nearby-poi-potential-merchants")
    public CommonResult<PageInfo<MerchantVisitPotentialVO>> queryNearbyPoiPotentialMerchants(
            @RequestBody @Validated NearbyPoiPotentialMerchantQuery queryVO) {
        log.info("查询指定位置的潜力值私海客户组，参数：{}", queryVO);

        // TODO: 实现查询逻辑
        // 参考net.summerfarm.crm.service.FollowUpRelationService.merchantNearby
        // 1. 根据省市区和POI位置信息，查询指定距离范围内的私海客户
        // 2. 返回客户列表

        return CommonResult.ok();
    }

    /**
     * 查询指定位置的推荐私海客户组
     *
     * @param queryVO 查询参数
     * @return 推荐的私海客户列表
     */
    @PostMapping("/query/recommend-potential-merchants")
    public CommonResult<PageInfo<MerchantVisitPotentialVO>> queryRecommendPotentialMerchants(
            @RequestBody @Validated RecommendPotentialMerchantQuery queryVO) {
        log.info("查询指定位置的推荐私海客户组，参数：{}", queryVO);

        // TODO: 实现查询逻辑
        // 参考net.summerfarm.crm.service.FollowUpRelationService.merchantNearby
        // 1. 根据省市区和POI位置信息，查询指定距离范围内的私海客户， topN个
        // 2. 返回客户列表

        return CommonResult.ok();
    }

    /**
     * 生成销售拜访计划草案
     */
    @PostMapping("/generate/visit-plan-draft")
    public CommonResult<Boolean> generateVisitPlanDraft(
            @RequestBody @Validated GenerateVisitPlanDraftInput input
    ) {
        log.info("生成销售拜访计划草案");

        // TODO: 实现生成逻辑

        return CommonResult.ok();
    }

    /**
     * 重新生成销售拜访计划
     */
    @PostMapping("/re-generate/visit-plan")
    public CommonResult<Boolean> reGenerateVisitPlan(
            @RequestBody @Validated ReGenerateVisitPlanInput input
    ) {
        log.info("重新生成销售拜访计划");

        // TODO: 实现生成逻辑

        return CommonResult.ok();
    }

    /**
     * 查询销售拜访目标信息
     *
     * @return 销售拜访目标信息
     */
    @PostMapping("/query/bd-visit-target")
    public CommonResult<BdVisitTargetVO> queryBdVisitTarget(@RequestBody BdVisitTargetQuery query) {
        return CommonResult.ok();
    }

    /**
     * 查询销售拜访目标指标列表
     *
     * @return 销售拜访目标指标列表
     */
    @PostMapping("/list/bd-visit-target-indicator")
    public CommonResult<List<BdVisitTargetIndicatorVO>> listBdVisitTargetIndicator(@RequestBody BdVisitTargetIndicatorListQuery query) {
        return CommonResult.ok();
    }

    /**
     * 查询销售拜访计划列表
     *
     * @param query
     * @return 销售拜访计划列表
     */
    @PostMapping("/list/bd-visit-plan")
    public CommonResult<List<BdVisitPlanVO>> listBdVisitPlan(@RequestBody @Validated BdVisitPlanListQuery query) {
        return CommonResult.ok();
    }

    /**
     * 生成推荐话术
     *
     * @param input
     * @return
     */
    @PostMapping("/generate/recommend-script")
    public CommonResult<String> generateRecommendScript(@RequestBody @Validated GenerateRecommendScriptInput input) {
        return CommonResult.ok();
    }

    /**
     * 修改拜访计划中客户的锁定状态
     *
     * @param input
     * @return
     */
    @PostMapping("/change/bd-visit-plan-lock-status")
    public CommonResult<Boolean> changeBdVisitPlanLockStatus(@RequestBody @Validated ChangeBdVisitPlanLockStatusInput input) {
        return CommonResult.ok();
    }

    /**
     * 保存拜访计划
     *
     * @param input
     * @return
     */
    @PostMapping("/save/bd-visit-plan")
    public CommonResult<Boolean> saveBdVisitPlan(@RequestBody @Validated SaveBdVisitPlanInput input) {
        return CommonResult.ok();
    }

    /**
     * 将客户添加到销售拜访计划
     *
     * @param input
     * @return
     */
    @PostMapping("/add/bd-visit-plan")
    public CommonResult<Boolean> addBdVisitPlan(@RequestBody @Validated AddBdVisitPlanInput input) {
        return CommonResult.ok();
    }

}
