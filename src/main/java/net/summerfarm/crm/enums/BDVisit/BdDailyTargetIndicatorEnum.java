package net.summerfarm.crm.enums.BDVisit;

/**
 * BD拜访目标指标枚举
 *
 * <AUTHOR>
 * @date 2025-01-14
 */
public class BdVisitTargetIndicatorEnum {

    /**
     * 指标状态
     */
    public enum IndicatorStatus {
        // 未完成
        INCOMPLETE(0, "未完成"),
        // 已完成
        COMPLETED(1, "已完成");

        private final Integer code;
        private final String desc;

        IndicatorStatus(Integer code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        public Integer getCode() {
            return code;
        }

        public String getDesc() {
            return desc;
        }
    }
}