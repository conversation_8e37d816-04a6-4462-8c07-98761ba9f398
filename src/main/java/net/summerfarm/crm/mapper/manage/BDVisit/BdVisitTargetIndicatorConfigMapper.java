package net.summerfarm.crm.mapper.manage.BDVisit;

import net.summerfarm.crm.model.entity.BDVisit.BdVisitTargetIndicatorConfigUpdate;
import net.summerfarm.crm.model.entity.BDVisit.BdVisitTargetIndicatorConfigQuery;
import net.summerfarm.crm.model.domain.BDVisit.BdVisitTargetIndicatorConfig;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 销售拜访目标指标配置 Mapper接口
 * <AUTHOR>
@Repository
public interface BdVisitTargetIndicatorConfigMapper {

    /**
     * 根据主键删除记录
     * @param id 主键
     * @return 影响行数
     */
    int deleteByPrimaryKey(Long id);

    /**
     * 选择性插入记录
     * @param record 记录对象
     * @return 影响行数
     */
    int insertSelective(BdVisitTargetIndicatorConfig record);

    /**
     * 批量插入记录
     * @param records 记录列表
     * @return 影响行数
     */
    int insertBatch(@Param("list") List<BdVisitTargetIndicatorConfig> records);

    /**
     * 根据主键查询记录
     * @param id 主键
     * @return 记录对象
     */
    BdVisitTargetIndicatorConfig selectByPrimaryKey(Long id);

    /**
     * 选择性更新记录
     * @param record 更新对象
     * @return 影响行数
     */
    int updateByPrimaryKeySelective(BdVisitTargetIndicatorConfigUpdate record);

    /**
     * 根据查询条件查询记录列表
     * @param query 查询条件
     * @return 记录列表
     */
    List<BdVisitTargetIndicatorConfig> list(BdVisitTargetIndicatorConfigQuery query);

}