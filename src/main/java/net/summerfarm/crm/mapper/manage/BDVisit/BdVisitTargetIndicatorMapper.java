package net.summerfarm.crm.mapper.manage.BDVisit;

import net.summerfarm.crm.model.entity.BDVisit.BdVisitTargetIndicatorUpdate;
import net.summerfarm.crm.model.entity.BDVisit.BdVisitTargetIndicatorQuery;
import net.summerfarm.crm.model.domain.BDVisit.BdVisitTargetIndicator;
import net.summerfarm.crm.model.domain.BDVisit.BdVisitTargetIndicatorSync;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 销售拜访目标指标 Mapper接口
 * <AUTHOR>
@Repository
public interface BdVisitTargetIndicatorMapper {

    /**
     * 根据主键删除记录
     * @param id 主键
     * @return 影响行数
     */
    int deleteByPrimaryKey(Long id);

    /**
     * 选择性插入记录
     * @param record 记录对象
     * @return 影响行数
     */
    int insertSelective(BdVisitTargetIndicator record);

    /**
     * 批量插入记录
     * @param records 记录列表
     * @return 影响行数
     */
    int insertBatch(@Param("list") List<BdVisitTargetIndicator> records);

    /**
     * 根据主键查询记录
     * @param id 主键
     * @return 记录对象
     */
    BdVisitTargetIndicator selectByPrimaryKey(Long id);

    /**
     * 选择性更新记录
     * @param record 更新对象
     * @return 影响行数
     */
    int updateByPrimaryKeySelective(BdVisitTargetIndicatorUpdate record);

    /**
     * 根据查询条件查询记录列表
     * @param query 查询条件
     * @return 记录列表
     */
    List<BdVisitTargetIndicator> list(BdVisitTargetIndicatorQuery query);

    /**
     * 批量更新指标当前值和状态
     * @param syncDataList 同步数据列表
     * @return 影响行数
     */
    int batchUpdateIndicatorValueAndStatus(@Param("list") List<BdVisitTargetIndicatorSync> syncDataList);

}