package net.summerfarm.crm.mapper.manage.BDVisit;

import net.summerfarm.crm.model.domain.BDVisit.BdVisitTarget;
import net.summerfarm.crm.model.entity.BDVisit.BdVisitTargetQuery;
import net.summerfarm.crm.model.entity.BDVisit.BdVisitTargetUpdate;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;
import java.time.LocalDate;
import java.util.List;

/**
 * 销售拜访目标 Mapper接口
 * <AUTHOR>
@Repository
public interface BdVisitTargetMapper {

    /**
     * 根据主键删除记录
     * @param id 主键
     * @return 影响行数
     */
    int deleteByPrimaryKey(Long id);

    /**
     * 选择性插入记录
     * @param record 记录对象
     * @return 影响行数
     */
    int insertSelective(BdVisitTarget record);

    /**
     * 批量插入记录
     * @param records 记录列表
     * @return 影响行数
     */
    int batchInsert(@Param("list") List<BdVisitTarget> records);

    /**
     * 根据主键查询记录
     * @param id 主键
     * @return 记录对象
     */
    BdVisitTarget selectByPrimaryKey(Long id);

    /**
     * 选择性更新记录
     * @param record 更新对象
     * @return 影响行数
     */
    int updateByPrimaryKeySelective(BdVisitTargetUpdate record);

    /**
     * 根据查询条件查询记录列表
     * @param query 查询条件
     * @return 记录列表
     */
    List<BdVisitTarget> list(BdVisitTargetQuery query);


    /**
     * 根据销售ID列表和日期范围查询销售拜访目标
     * @param bdIds 销售ID列表
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 销售拜访目标列表
     */
    List<BdVisitTarget> selectByBdIdsAndDateRange(@Param("bdIds") List<Integer> bdIds, @Param("startDate") LocalDate startDate, @Param("endDate") LocalDate endDate);

}