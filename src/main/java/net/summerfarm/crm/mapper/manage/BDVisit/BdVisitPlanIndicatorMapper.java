package net.summerfarm.crm.mapper.manage.BDVisit;

import net.summerfarm.crm.model.entity.BDVisit.BdVisitPlanIndicatorUpdate;
import net.summerfarm.crm.model.entity.BDVisit.BdVisitPlanIndicatorQuery;
import net.summerfarm.crm.model.domain.BDVisit.BdVisitPlanIndicator;
import net.summerfarm.crm.model.domain.BDVisit.BdVisitPlanIndicatorSync;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 销售拜访计划指标 Mapper接口
 * <AUTHOR>
@Repository
public interface BdVisitPlanIndicatorMapper {

    /**
     * 根据主键删除记录
     * @param id 主键
     * @return 影响行数
     */
    int deleteByPrimaryKey(Long id);

    /**
     * 选择性插入记录
     * @param record 记录对象
     * @return 影响行数
     */
    int insertSelective(BdVisitPlanIndicator record);

    /**
     * 批量插入记录
     * @param records 记录列表
     * @return 影响行数
     */
    int insertBatch(@Param("list") List<BdVisitPlanIndicator> records);

    /**
     * 根据主键查询记录
     * @param id 主键
     * @return 记录对象
     */
    BdVisitPlanIndicator selectByPrimaryKey(Long id);

    /**
     * 选择性更新记录
     * @param record 更新对象
     * @return 影响行数
     */
    int updateByPrimaryKeySelective(BdVisitPlanIndicatorUpdate record);

    /**
     * 根据查询条件查询记录列表
     * @param query 查询条件
     * @return 记录列表
     */
    List<BdVisitPlanIndicator> list(BdVisitPlanIndicatorQuery query);

    /**
     * 批量更新指标当前值和潜力值
     * @param syncDataList 同步数据列表
     * @return 影响行数
     */
    int batchUpdateIndicatorValueAndPotential(@Param("list") List<BdVisitPlanIndicatorSync> syncDataList);

    /**
     * 根据门店ID批量更新潜力值
     * @param indicators 指标列表
     * @return 影响行数
     */
    int batchUpdatePotentialValueByMerchantId(@Param("list") List<BdVisitPlanIndicator> indicators);

    /**
     * 根据门店ID查询拜访计划指标
     * @param merchantIds 门店ID列表
     * @return 拜访计划指标列表
     */
    List<BdVisitPlanIndicator> selectByMerchantIds(@Param("merchantIds") List<Long> merchantIds);

    /**
     * 根据销售ID查询拜访计划指标
     * @param salesId 销售ID
     * @return 拜访计划指标列表
     */
    List<BdVisitPlanIndicator> selectBySalesId(@Param("salesId") Integer salesId);

    /**
     * 根据目标指标ID列表查询拜访计划指标
     * @param targetIndicatorIds 目标指标ID列表
     * @return 拜访计划指标列表
     */
    List<BdVisitPlanIndicator> selectByTargetIndicatorIds(@Param("targetIndicatorIds") List<Long> targetIndicatorIds);

}