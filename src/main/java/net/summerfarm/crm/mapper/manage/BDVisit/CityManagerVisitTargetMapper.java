package net.summerfarm.crm.mapper.manage.BDVisit;

import net.summerfarm.crm.model.entity.BDVisit.CityManagerVisitTargetUpdate;
import net.summerfarm.crm.model.entity.BDVisit.CityManagerVisitTargetQuery;
import net.summerfarm.crm.model.domain.BDVisit.CityManagerVisitTarget;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 主管销售拜访目标 Mapper接口
 * <AUTHOR>
@Repository
public interface CityManagerVisitTargetMapper {

    /**
     * 根据主键删除记录
     * @param id 主键
     * @return 影响行数
     */
    int deleteByPrimaryKey(Long id);

    /**
     * 选择性插入记录
     * @param record 记录对象
     * @return 影响行数
     */
    int insertSelective(CityManagerVisitTarget record);

    /**
     * 批量插入记录
     * @param records 记录列表
     * @return 影响行数
     */
    int insertBatch(@Param("list") List<CityManagerVisitTarget> records);

    /**
     * 根据主键查询记录
     * @param id 主键
     * @return 记录对象
     */
    CityManagerVisitTarget selectByPrimaryKey(Long id);

    /**
     * 选择性更新记录
     * @param record 更新对象
     * @return 影响行数
     */
    int updateByPrimaryKeySelective(CityManagerVisitTargetUpdate record);

    /**
     * 根据查询条件查询记录列表
     * @param query 查询条件
     * @return 记录列表
     */
    List<CityManagerVisitTarget> list(CityManagerVisitTargetQuery query);

}