package net.summerfarm.crm.mapper.manage;

import net.summerfarm.common.annotation.RequiresDataPermission;
import net.summerfarm.crm.model.domain.FollowUpRecord;
import net.summerfarm.crm.model.vo.FollowUpRecordVO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Collection;
import java.util.Date;
import java.util.List;

@Repository
public interface FollowUpRecordMapper {
    /**
     * 插入拜访记录
     * @param record 插入内容
     * @return 0或1
     */
    int insertSelective(FollowUpRecord record);

    /**
     * 根据拜访记录id查询信息
     * @param id 拜访记录id
     * @return 拜访记录信息
     */
    FollowUpRecord selectById(Integer id);

    /**
     * 修改拜访记录信息
     * @param record 拜访记录
     * @return 0或1
     */
    int updateById(FollowUpRecord record);

    /**
     * 查询拜访记录
     * @param selectKeys 查询条件
     * @return 拜访记录
     */
    @RequiresDataPermission(originalField = "t.area_no")
    List<FollowUpRecordVO> selectVO(FollowUpRecord selectKeys);

    /**
     * 查询周期内拜访记录
     * @param selectKeys 查询条件
     * @return 周期内拜访记录
     */
    List<FollowUpRecordVO> selectByStart(FollowUpRecordVO selectKeys);

    /**
     * 获取bd填写的商户备注
     * @param mId 商户id
     * @param couponId 卡券id
     * @return 商户备注
     */
    FollowUpRecord noteDetails(@Param("mId") int mId, @Param("couponId") Integer couponId);

    /**
     * 查询主管的陪访记录
     * @param adminId 拜访人id
     * @param date 拜访日期
     * @param contactId 联系人id
     * @param escortAdminId 陪访人id
     * @return 陪访记录
     */
    List<FollowUpRecord> selectEscortRecord(@Param("adminId") Integer adminId,@Param("date") LocalDate date,
                                            @Param("contactId") Integer contactId,@Param("escortAdminId") Integer escortAdminId);

    /**
     * 获取拜访计划关联的拜访记录
     * @param visitPlanId 拜访计划id
     * @return 拜访记录
     */
    FollowUpRecord selectByVisitPlanId(@Param("visitPlanId") Long visitPlanId,@Param("escortVisitPlanId") Long escortVisitPlanId);

    /**
     * 上个小时拜访记录
     *
     * @return {@link List}<{@link FollowUpRecord}>
     */
    List<FollowUpRecord> selectLastHourRecord();

    /**
     * 查询任务拜访
     *
     * @return {@link FollowUpRecord}
     */
    FollowUpRecord selectTaskFollowUp(@Param("taskId")Integer taskId,@Param("mId")Integer mId);

    /**
     * 查询最新的拜访记录
     */
    FollowUpRecord selectLatestRecord(@Param("mId")Long mId);

    /**
     * 根据客户id和销售id查询最新的拜访记录
     */
    FollowUpRecord selectLatestRecordByMidAndAdminId(@Param("mId") Long mId, @Param("adminId") Integer adminId);

    /**
     * 根据拜访时间排序,查找上一次拜访记录(同客户同销售)
     */
    Long selectLastRecordIdById(@Param("id") long id);

    List<FollowUpRecord> selectByFollowUpWayInAndAddTimeBetween(
            @Param("mId") Long mId,
            @Param("bdId") Integer bdId,
            @Param("followUpWayCollection") Collection<String> followUpWayCollection,
            @Param("minAddTime") LocalDateTime minAddTime,
            @Param("maxAddTime") LocalDateTime maxAddTime);

    /**
     * 根据mId、adminId、addTime、condition查询是否存在拜访记录
     * 
     * @param mId 客户id
     * @param adminId 销售id
     * @param addTime 拜访时间
     * @param condition 拜访情况
     * @return 拜访记录数量
     */
    int countByMIdAndAdminIdAndAddTimeAndCondition(@Param("mId") Long mId, @Param("adminId") Integer adminId, @Param("addTime") Date addTime, @Param("condition") String condition);

    /**
     * 批量查询指定时间范围内有拜访记录的门店ID
     * 
     * @param merchantIds 门店ID列表
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 有拜访记录的门店ID集合
     */
    List<Long> selectVisitedMerchantIds(@Param("merchantIds") List<Long> merchantIds, @Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime);

}