package net.summerfarm.crm.mapper.repository.BDVisit;

import com.baomidou.mybatisplus.extension.repository.CrudRepository;
import net.summerfarm.crm.mapper.offline.BDVisit.BdVisitPlanIndicatorSyncMapper;
import net.summerfarm.crm.model.domain.BDVisit.BdVisitPlanIndicatorSync;
import org.springframework.stereotype.Repository;

@Repository
public class BdVisitPlanIndicatorSyncRepository extends CrudRepository<BdVisitPlanIndicatorSyncMapper, BdVisitPlanIndicatorSync> {
}