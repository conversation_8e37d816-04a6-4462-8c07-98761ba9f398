package net.summerfarm.crm.mapper.repository.BDVisit;

import lombok.extern.slf4j.Slf4j;
import net.summerfarm.crm.mapper.manage.BDVisit.BdVisitTargetIndicatorMapper;
import net.summerfarm.crm.model.domain.BDVisit.BdVisitTargetIndicatorSync;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * BD拜访目标指标Repository
 * 负责数据访问抽象、复杂查询封装、数据转换
 *
 * <AUTHOR>
 */
@Slf4j
@Repository
public class BdVisitTargetIndicatorRepository {

    @Resource
    private BdVisitTargetIndicatorMapper bdVisitTargetIndicatorMapper;

    /**
     * 批量更新指标当前值和状态
     * 过滤有效数据并执行批量更新操作
     *
     * @param syncList 同步数据列表
     * @return 影响的行数
     */
    public int batchUpdateIndicatorValueAndStatus(List<BdVisitTargetIndicatorSync> syncList) {
        if (CollectionUtils.isEmpty(syncList)) {
            log.warn("批量更新指标：同步数据列表为空");
            return 0;
        }

        // 过滤有效的同步数据（指标当前值不为空）
        List<BdVisitTargetIndicatorSync> validSyncList = syncList.stream()
                .filter(sync -> sync.getIndicatorCurrentValue() != null)
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(validSyncList)) {
            log.warn("批量更新指标：过滤后无有效数据，原始数据量：{}", syncList.size());
            return 0;
        }

        log.info("批量更新指标：原始数据量：{}，有效数据量：{}", syncList.size(), validSyncList.size());

        // 调用Mapper执行批量更新
        int affectedRows = bdVisitTargetIndicatorMapper.batchUpdateIndicatorValueAndStatus(validSyncList);
        
        log.info("批量更新指标完成：影响行数：{}", affectedRows);
        return affectedRows;
    }
}