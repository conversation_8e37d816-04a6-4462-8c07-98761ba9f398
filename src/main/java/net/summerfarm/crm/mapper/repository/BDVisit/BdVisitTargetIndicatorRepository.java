package net.summerfarm.crm.mapper.repository.BDVisit;

import lombok.extern.slf4j.Slf4j;
import net.summerfarm.crm.mapper.manage.BDVisit.BdVisitTargetIndicatorMapper;
import net.summerfarm.crm.model.domain.BDVisit.BdVisitTargetIndicator;
import net.summerfarm.crm.model.domain.BDVisit.BdVisitTargetIndicatorSync;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;
import java.util.List;
import java.util.stream.Collectors;
import javax.annotation.Resource;

/**
 * BD拜访目标指标Repository
 * 负责数据访问抽象、复杂查询封装、数据转换
 *
 * <AUTHOR>
 */
@Slf4j
@Repository
public class BdVisitTargetIndicatorRepository {

    @Resource
    private BdVisitTargetIndicatorMapper bdVisitTargetIndicatorMapper;

    /**
     * 批量更新指标当前值和状态
     * 过滤有效数据并执行批量更新操作
     *
     * @param syncList 同步数据列表
     * @return 影响的行数
     */
    public int batchUpdateIndicatorValueAndStatus(List<BdVisitTargetIndicatorSync> syncList) {
        if (CollectionUtils.isEmpty(syncList)) {
            log.warn("批量更新指标：同步数据列表为空");
            return 0;
        }

        // 过滤有效的同步数据（指标当前值不为空）
        List<BdVisitTargetIndicatorSync> validSyncList = syncList.stream()
                .filter(sync -> sync.getIndicatorCurrentValue() != null)
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(validSyncList)) {
            log.warn("批量更新指标：过滤后无有效数据，原始数据量：{}", syncList.size());
            return 0;
        }

        log.info("批量更新指标：原始数据量：{}，有效数据量：{}", syncList.size(), validSyncList.size());

        // 调用Mapper执行批量更新
        int affectedRows = bdVisitTargetIndicatorMapper.batchUpdateIndicatorValueAndStatus(validSyncList);
        
        log.info("批量更新指标完成：影响行数：{}", affectedRows);
        return affectedRows;
    }

    /**
     * 批量插入销售拜访目标指标
     *
     * @param indicators 销售拜访目标指标列表
     * @return 影响行数
     */
    public int batchInsert(List<BdVisitTargetIndicator> indicators) {
        if (CollectionUtils.isEmpty(indicators)) {
            log.warn("批量插入指标：指标列表为空");
            return 0;
        }
        
        log.info("批量插入指标：数据量：{}", indicators.size());
        int affectedRows = bdVisitTargetIndicatorMapper.batchInsert(indicators);
        log.info("批量插入指标完成：影响行数：{}", affectedRows);
        return affectedRows;
    }
}