package net.summerfarm.crm.mapper.repository.BDVisit;

import com.baomidou.mybatisplus.extension.repository.CrudRepository;
import net.summerfarm.crm.mapper.offline.BDVisit.BdVisitTargetIndicatorSyncMapper;
import net.summerfarm.crm.model.domain.BDVisit.BdVisitTargetIndicatorSync;
import org.springframework.stereotype.Repository;

@Repository
public class BdVisitTargetIndicatorSyncRepository extends CrudRepository<BdVisitTargetIndicatorSyncMapper, BdVisitTargetIndicatorSync> {
}