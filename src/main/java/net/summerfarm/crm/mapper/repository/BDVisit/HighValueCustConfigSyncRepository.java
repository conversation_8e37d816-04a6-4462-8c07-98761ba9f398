package net.summerfarm.crm.mapper.repository.BDVisit;

import com.baomidou.mybatisplus.extension.repository.CrudRepository;
import net.summerfarm.crm.mapper.offline.HighValueCustConfigSyncMapper;
import net.summerfarm.crm.model.domain.BDVisit.HighValueCustConfigSync;
import org.springframework.stereotype.Repository;

@Repository
public class HighValueCustConfigSyncRepository extends CrudRepository<HighValueCustConfigSyncMapper, HighValueCustConfigSync> {
}