package net.summerfarm.crm.mapper.repository.BDVisit;

import net.summerfarm.crm.mapper.manage.BDVisit.BdVisitTargetMapper;
import net.summerfarm.crm.model.domain.BDVisit.BdVisitTarget;
import net.xianmu.common.exception.BizException;
import org.springframework.stereotype.Repository;
import java.time.LocalDate;
import java.util.List;
import javax.annotation.Resource;


import lombok.extern.slf4j.Slf4j;
import net.summerfarm.crm.model.entity.BDVisit.BdVisitTargetQuery;
import net.summerfarm.crm.model.entity.BDVisit.BdVisitTargetUpdate;
import org.springframework.util.CollectionUtils;

import java.util.Collections;

/**
 * BD拜访目标Repository
 * 负责数据访问抽象、复杂查询封装、数据转换
 *
 * <AUTHOR>
 */
@Slf4j
@Repository
public class BdVisitTargetRepository {

    @Resource
    private BdVisitTargetMapper bdVisitTargetMapper;

    /**
     * 批量插入销售拜访目标
     *
     * @param targets 销售拜访目标列表
     * @return 影响行数
     */
    public int batchInsert(List<BdVisitTarget> targets) {
        if (targets == null || targets.isEmpty()) {
            return 0;
        }
        return bdVisitTargetMapper.batchInsert(targets);
    }

    /**
     * 根据销售ID列表和日期范围查询销售拜访目标
     *
     * @param bdIds 销售ID列表
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 销售拜访目标列表
     */
    public List<BdVisitTarget> selectByBdIdsAndDateRange(List<Integer> bdIds, LocalDate startDate, LocalDate endDate) {
        if (bdIds == null || bdIds.isEmpty() || startDate == null || endDate == null) {
            throw new BizException("销售id列表、开始日期、结束日期不能为空");
        }
        return bdVisitTargetMapper.selectByBdIdsAndDateRange(bdIds, startDate, endDate);
    }

    /*
     * 根据主键查询记录
     *
     * @param id 主键
     * @return 记录对象
     */
    public BdVisitTarget selectByPrimaryKey(Long id) {
        if (id == null) {
            log.warn("根据主键查询拜访目标：主键为空");
            return null;
        }
        
        try {
            BdVisitTarget result = bdVisitTargetMapper.selectByPrimaryKey(id);
            log.debug("根据主键查询拜访目标，主键: {}, 结果: {}", id, result != null ? "找到" : "未找到");
            return result;
        } catch (Exception e) {
            log.error("根据主键查询拜访目标失败，主键: {}", id, e);
            throw e;
        }
    }

    /**
     * 根据查询条件查询记录列表
     *
     * @param query 查询条件
     * @return 记录列表
     */
    public List<BdVisitTarget> list(BdVisitTargetQuery query) {
        if (query == null) {
            log.warn("查询拜访目标列表：查询条件为空");
            return Collections.emptyList();
        }
        
        try {
            List<BdVisitTarget> result = bdVisitTargetMapper.list(query);
            log.debug("查询拜访目标列表，查询条件: {}, 结果数量: {}", query, result != null ? result.size() : 0);
            return result != null ? result : Collections.emptyList();
        } catch (Exception e) {
            log.error("查询拜访目标列表失败，查询条件: {}", query, e);
            throw e;
        }
    }

    /**
     * 根据销售ID和目标日期查询拜访目标
     *
     * @param bdId 销售ID
     * @param targetDate 目标日期
     * @return 拜访目标列表
     */
    public List<BdVisitTarget> selectByBdIdAndTargetDate(Integer bdId, LocalDate targetDate) {
        if (bdId == null || targetDate == null) {
            log.warn("根据销售ID和目标日期查询拜访目标：参数为空，bdId: {}, targetDate: {}", bdId, targetDate);
            return Collections.emptyList();
        }
        
        try {
            BdVisitTargetQuery query = new BdVisitTargetQuery();
            query.setBdId(bdId);
            query.setTargetDate(targetDate);
            
            List<BdVisitTarget> result = bdVisitTargetMapper.list(query);
            log.debug("根据销售ID和目标日期查询拜访目标，bdId: {}, targetDate: {}, 结果数量: {}", 
                    bdId, targetDate, result != null ? result.size() : 0);
            return result != null ? result : Collections.emptyList();
        } catch (Exception e) {
            log.error("根据销售ID和目标日期查询拜访目标失败，bdId: {}, targetDate: {}", bdId, targetDate, e);
            throw e;
        }
    }

    /**
     * 选择性插入记录
     *
     * @param record 记录对象
     * @return 影响行数
     */
    public int insertSelective(BdVisitTarget record) {
        if (record == null) {
            log.warn("插入拜访目标：记录对象为空");
            return 0;
        }
        
        try {
            int result = bdVisitTargetMapper.insertSelective(record);
            log.debug("插入拜访目标，记录: {}, 影响行数: {}", record, result);
            return result;
        } catch (Exception e) {
            log.error("插入拜访目标失败，记录: {}", record, e);
            throw e;
        }
    }

    /**
     * 批量插入记录
     *
     * @param records 记录列表
     * @return 影响行数
     */
    public int insertBatch(List<BdVisitTarget> records) {
        if (CollectionUtils.isEmpty(records)) {
            log.warn("批量插入拜访目标：记录列表为空");
            return 0;
        }
        
        try {
            int result = bdVisitTargetMapper.batchInsert(records);
            log.debug("批量插入拜访目标，记录数量: {}, 影响行数: {}", records.size(), result);
            return result;
        } catch (Exception e) {
            log.error("批量插入拜访目标失败，记录数量: {}", records.size(), e);
            throw e;
        }
    }

    /**
     * 选择性更新记录
     *
     * @param record 更新对象
     * @return 影响行数
     */
    public int updateByPrimaryKeySelective(BdVisitTargetUpdate record) {
        if (record == null || record.getId() == null) {
            log.warn("更新拜访目标：记录对象或主键为空");
            return 0;
        }
        
        try {
            int result = bdVisitTargetMapper.updateByPrimaryKeySelective(record);
            log.debug("更新拜访目标，记录: {}, 影响行数: {}", record, result);
            return result;
        } catch (Exception e) {
            log.error("更新拜访目标失败，记录: {}", record, e);
            throw e;
        }
    }

    /**
     * 根据主键删除记录
     *
     * @param id 主键
     * @return 影响行数
     */
    public int deleteByPrimaryKey(Long id) {
        if (id == null) {
            log.warn("删除拜访目标：主键为空");
            return 0;
        }
        
        try {
            int result = bdVisitTargetMapper.deleteByPrimaryKey(id);
            log.debug("删除拜访目标，主键: {}, 影响行数: {}", id, result);
            return result;
        } catch (Exception e) {
            log.error("删除拜访目标失败，主键: {}", id, e);
            throw e;
        }
    }
}