package net.summerfarm.crm.mapper.repository;

import cn.hutool.core.util.ObjectUtil;
import net.summerfarm.crm.facade.ContactQueryFacade;
import net.summerfarm.crm.mapper.manage.FollowUpRecordMapper;
import net.summerfarm.crm.mapper.manage.MerchantMapper;
import net.summerfarm.crm.model.domain.FollowUpRecord;
import net.summerfarm.crm.model.dto.ContactDto;
import net.summerfarm.crm.model.vo.FollowUpRecordVO;
import net.summerfarm.crm.model.vo.MerchantVO;
import net.xianmu.usercenter.client.merchant.resp.MerchantStoreAndExtendResp;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;

import static net.xianmu.usercenter.client.merchant.enums.RegionalOrganizationEnums.Size.ADMIN;
import static net.xianmu.usercenter.client.merchant.enums.RegionalOrganizationEnums.Size.MERCHANT;

/**
 * <AUTHOR>
 */
@Service
public class FollowUpRecordRepository {

    @Resource
    private MerchantExtendsRepository merchantExtendsRepository;

    @Resource
    private FollowUpRecordMapper followUpRecordMapper;

    @Resource
    private ContactQueryFacade contactQueryFacade;

    @Resource
    private MerchantMapper  merchantMapper;

    /**
     * 获取拜访记录详情
     *
     * @return {@link FollowUpRecordVO}
     */
    public FollowUpRecordVO getFollowUpRecordVO(Long id, List<Integer> areaNos) {
        FollowUpRecord followUpRecord = followUpRecordMapper.selectById(id.intValue());
        if (ObjectUtils.isEmpty(followUpRecord)) {
            return null;
        }
        MerchantStoreAndExtendResp merchantStoreAndExtendResp =  merchantExtendsRepository.getAuthMerchantExtendDTO(followUpRecord.getmId(),areaNos);
        if(Objects.isNull(merchantStoreAndExtendResp)){
            return null;
        }
        MerchantVO merchantVO = merchantMapper.selectMerchantByMid(followUpRecord.getmId());
        ContactDto contactDto = contactQueryFacade.getMerchantContactById(Long.valueOf(followUpRecord.getContactId()));
        return convert(followUpRecord,merchantStoreAndExtendResp,contactDto,merchantVO);
    }

    private FollowUpRecordVO convert(FollowUpRecord followUpRecord,MerchantStoreAndExtendResp merchantStoreAndExtendResp,ContactDto contactDto,MerchantVO merchantVO) {
        FollowUpRecordVO followUpRecordVO = new FollowUpRecordVO();
        if (ObjectUtils.isNotEmpty(contactDto)){
            followUpRecordVO.setPhone(contactDto.getPhone());
            followUpRecordVO.setAddress(contactDto.getCity() + " " + contactDto.getArea() + " " + contactDto.getAddress() + (contactDto.getHouseNumber() != null ? " " + contactDto.getHouseNumber() : ""));
            followUpRecordVO.setAreaNo(contactDto.getAreaNo());
        }
        followUpRecordVO.setMname(merchantStoreAndExtendResp.getStoreName());
        followUpRecordVO.setmSize(ObjectUtil.equal(merchantStoreAndExtendResp.getSize(), ADMIN.getCode()) ? ADMIN.getDesc() : MERCHANT.getDesc());
        followUpRecordVO.setAdminName(followUpRecord.getAdminName());
        followUpRecordVO.setGrade(merchantVO.getGrade());
        followUpRecordVO.setLocation(followUpRecord.getLocation());
        followUpRecordVO.setmId(followUpRecord.getmId());
        followUpRecordVO.setmLifecycle(followUpRecord.getmLifecycle());
        followUpRecordVO.setmTag(followUpRecord.getmTag());
        followUpRecordVO.setmLastOrderTime(followUpRecord.getmLastOrderTime());
        followUpRecordVO.setId(followUpRecord.getId());
        followUpRecordVO.setAdminId(followUpRecord.getAdminId());
        followUpRecordVO.setAdminName(followUpRecord.getAdminName());
        followUpRecordVO.setCreator(followUpRecord.getCreator());
        followUpRecordVO.setFollowUpWay(followUpRecord.getFollowUpWay());
        followUpRecordVO.setFollowUpPic(followUpRecord.getFollowUpPic());
        followUpRecordVO.setStatus(followUpRecord.getStatus());
        followUpRecordVO.setPriority(followUpRecord.getPriority());
        followUpRecordVO.setCondition(followUpRecord.getCondition());
        followUpRecordVO.setAddTime(followUpRecord.getAddTime());
        followUpRecordVO.setContactId(followUpRecord.getContactId());
        followUpRecordVO.setNextFollowTime(followUpRecord.getNextFollowTime());
        followUpRecordVO.setExpectedContent(followUpRecord.getExpectedContent());
        followUpRecordVO.setVisitObjective(followUpRecord.getVisitObjective());
        followUpRecordVO.setVisitType(followUpRecord.getVisitType());
        followUpRecordVO.setEscortAdminId(followUpRecord.getEscortAdminId());
        followUpRecordVO.setOperateStatus(followUpRecord.getOperateStatus());
        followUpRecordVO.setWhetherRemark(followUpRecord.getWhetherRemark());
        followUpRecordVO.setLocation(followUpRecord.getLocation());
        followUpRecordVO.setKpId(followUpRecord.getKpId());
        followUpRecordVO.setPoiNote(followUpRecord.getPoiNote());
        followUpRecordVO.setVisitPlanId(followUpRecord.getVisitPlanId());
        followUpRecordVO.setEscortVisitPlanId(followUpRecord.getEscortVisitPlanId());
        followUpRecordVO.setAccountId(followUpRecord.getAccountId());
        followUpRecordVO.setFeedback(followUpRecord.getFeedback());
        followUpRecordVO.setFeedbackTime(followUpRecord.getFeedbackTime());
        followUpRecordVO.setPoiUpdateFlag(followUpRecord.getPoiUpdateFlag());
        followUpRecordVO.setDoorPic(merchantVO.getDoorPic());
        return followUpRecordVO;
    }

    /**
     * 批量查询指定时间范围内有拜访记录的门店ID
     * 
     * @param merchantIds 门店ID列表
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 有拜访记录的门店ID集合
     */
    public List<Long> getVisitedMerchantIds(List<Long> merchantIds, LocalDateTime startTime, LocalDateTime endTime) {
        return followUpRecordMapper.selectVisitedMerchantIds(merchantIds, startTime, endTime);
    }

}
