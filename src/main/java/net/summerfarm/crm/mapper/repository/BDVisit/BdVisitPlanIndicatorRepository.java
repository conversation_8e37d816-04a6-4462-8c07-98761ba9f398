package net.summerfarm.crm.mapper.repository.BDVisit;

import lombok.extern.slf4j.Slf4j;
import net.summerfarm.crm.mapper.manage.BDVisit.BdVisitPlanIndicatorMapper;
import net.summerfarm.crm.mapper.offline.BDVisit.BdVisitPlanIndicatorSyncMapper;
import net.summerfarm.crm.model.domain.BDVisit.BdVisitPlanIndicator;
import net.summerfarm.crm.model.domain.BDVisit.BdVisitPlanIndicatorSync;
import net.summerfarm.crm.model.entity.BDVisit.BdVisitPlanIndicatorUpdate;
import net.summerfarm.crm.model.entity.BDVisit.BdVisitPlanIndicatorQuery;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * BD拜访计划指标Repository
 * 负责数据访问抽象、复杂查询封装、数据转换
 *
 * <AUTHOR>
 */
@Slf4j
@Repository
public class BdVisitPlanIndicatorRepository {

    @Resource
    private BdVisitPlanIndicatorMapper bdVisitPlanIndicatorMapper;
    
    @Resource
    private BdVisitPlanIndicatorSyncMapper bdVisitPlanIndicatorSyncMapper;

    /**
     * 批量插入记录
     *
     * @param records 记录列表
     * @return 影响行数
     */
    public int insertBatch(List<BdVisitPlanIndicator> records) {
        if (CollectionUtils.isEmpty(records)) {
            log.warn("批量插入拜访计划指标：记录列表为空");
            return 0;
        }
        
        try {
            int result = bdVisitPlanIndicatorMapper.insertBatch(records);
            log.debug("批量插入拜访计划指标，记录数量: {}, 影响行数: {}", records.size(), result);
            return result;
        } catch (Exception e) {
            log.error("批量插入拜访计划指标失败，记录数量: {}", records.size(), e);
            throw e;
        }
    }

    /**
     * 批量更新指标当前值和潜力值
     * 过滤有效数据并执行批量更新操作
     *
     * @param syncList 同步数据列表
     * @return 影响的行数
     */
    public int batchUpdateIndicatorValueAndPotential(List<BdVisitPlanIndicatorSync> syncList) {
        if (CollectionUtils.isEmpty(syncList)) {
            log.warn("批量更新计划指标：同步数据列表为空");
            return 0;
        }

        // 过滤有效的同步数据（指标当前值或潜力值不为空）
        List<BdVisitPlanIndicatorSync> validSyncList = syncList.stream()
                .filter(sync -> sync.getIndicatorCurrentValue() != null || sync.getIndicatorPotentialValue() != null)
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(validSyncList)) {
            log.warn("批量更新计划指标：过滤后无有效数据，原始数据量：{}", syncList.size());
            return 0;
        }

        log.info("批量更新计划指标：原始数据量：{}，有效数据量：{}", syncList.size(), validSyncList.size());

        // 调用Mapper执行批量更新
        int affectedRows = bdVisitPlanIndicatorMapper.batchUpdateIndicatorValueAndPotential(validSyncList);
        
        log.info("批量更新计划指标完成：影响行数：{}", affectedRows);
        return affectedRows;
    }

    /**
     * 根据门店ID和目标指标ID批量更新潜力值
     *
     * @param indicators 指标更新列表
     * @return 影响的行数
     */
    public int batchUpdatePotentialValueByMerchantId(List<BdVisitPlanIndicatorUpdate> indicators) {
        if (CollectionUtils.isEmpty(indicators)) {
            log.warn("批量更新门店潜力值：指标列表为空");
            return 0;
        }

        // 过滤有效的指标数据（门店ID、目标指标ID和潜力值不为空）
        List<BdVisitPlanIndicatorUpdate> validIndicators = indicators.stream()
                .filter(indicator -> indicator.getMId() != null 
                        && indicator.getBdVisitTargetIndicatorId() != null 
                        && indicator.getIndicatorPotentialValue() != null)
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(validIndicators)) {
            log.warn("批量更新门店潜力值：过滤后无有效数据，原始数据量：{}", indicators.size());
            return 0;
        }

        // region 批量查询现有记录，过滤掉不存在的记录
        // 批量查询所有相关记录
        List<Long> merchantIds = validIndicators.stream()
                .map(BdVisitPlanIndicatorUpdate::getMId)
                .distinct()
                .collect(Collectors.toList());
        
        List<Long> targetIndicatorIds = validIndicators.stream()
                .map(BdVisitPlanIndicatorUpdate::getBdVisitTargetIndicatorId)
                .distinct()
                .collect(Collectors.toList());
        
        List<BdVisitPlanIndicator> allExistingRecords = bdVisitPlanIndicatorMapper.selectByMerchantIdsAndTargetIndicatorIds(merchantIds, targetIndicatorIds);
        
        // 构建存在记录的key集合
        Set<String> existingKeys = allExistingRecords.stream()
                .map(record -> record.getMId() + "_" + record.getBdVisitTargetIndicatorId())
                .collect(Collectors.toSet());
        
        // 过滤出存在的记录
        List<BdVisitPlanIndicatorUpdate> existingIndicators = validIndicators.stream()
                .filter(indicator -> {
                    String key = indicator.getMId() + "_" + indicator.getBdVisitTargetIndicatorId();
                    return existingKeys.contains(key);
                })
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(existingIndicators)) {
            log.warn("批量更新门店潜力值：过滤后无存在的记录，有效数据量：{}", validIndicators.size());
            return 0;
        }
        // endregion

        log.info("批量更新门店潜力值：原始数据量：{}，有效数据量：{}，存在记录数量：{}", 
                indicators.size(), validIndicators.size(), existingIndicators.size());

        // 调用Mapper执行批量更新
        int affectedRows = bdVisitPlanIndicatorMapper.batchUpdatePotentialValueByMerchantId(existingIndicators);
        
        log.info("批量更新门店潜力值完成：影响行数：{}", affectedRows);
        return affectedRows;
    }

    /**
     * 根据门店ID查询拜访计划指标
     *
     * @param merchantIds 门店ID列表
     * @return 拜访计划指标列表
     */
    public List<BdVisitPlanIndicator> selectByMerchantIds(List<Long> merchantIds) {
        if (CollectionUtils.isEmpty(merchantIds)) {
            log.warn("根据门店ID查询拜访计划指标：门店ID列表为空");
            return null;
        }

        try {
            List<BdVisitPlanIndicator> result = bdVisitPlanIndicatorMapper.selectByMerchantIds(merchantIds);
            log.debug("根据门店ID查询拜访计划指标：门店数量：{}，结果数量：{}", 
                    merchantIds.size(), result != null ? result.size() : 0);
            return result;
        } catch (Exception e) {
            log.error("根据门店ID查询拜访计划指标失败：门店数量：{}", merchantIds.size(), e);
            throw e;
        }
    }

    /**
     * 根据销售ID查询拜访计划指标
     *
     * @param salesId 销售ID
     * @return 拜访计划指标列表
     */
    public List<BdVisitPlanIndicator> selectBySalesId(Integer salesId) {
        if (salesId == null) {
            log.warn("根据销售ID查询拜访计划指标：销售ID为空");
            return null;
        }

        try {
            List<BdVisitPlanIndicator> result = bdVisitPlanIndicatorMapper.selectBySalesId(salesId);
            log.debug("根据销售ID查询拜访计划指标：销售ID：{}，结果数量：{}", 
                    salesId, result != null ? result.size() : 0);
            return result;
        } catch (Exception e) {
            log.error("根据销售ID查询拜访计划指标失败：销售ID：{}", salesId, e);
            throw e;
        }
    }

    /**
     * 根据目标指标ID列表查询拜访计划指标
     *
     * @param targetIndicatorIds 目标指标ID列表
     * @return 拜访计划指标列表
     */
    public List<BdVisitPlanIndicator> selectByTargetIndicatorIds(List<Long> targetIndicatorIds) {
        if (CollectionUtils.isEmpty(targetIndicatorIds)) {
            log.warn("根据目标指标ID查询拜访计划指标：目标指标ID列表为空");
            return null;
        }

        try {
            List<BdVisitPlanIndicator> result = bdVisitPlanIndicatorMapper.selectByTargetIndicatorIds(targetIndicatorIds);
            log.debug("根据目标指标ID查询拜访计划指标：目标指标数量：{}，结果数量：{}", 
                    targetIndicatorIds.size(), result != null ? result.size() : 0);
            return result;
        } catch (Exception e) {
            log.error("根据目标指标ID查询拜访计划指标失败：目标指标数量：{}", targetIndicatorIds.size(), e);
            throw e;
        }
     }
     
     // ========== 同步数据相关方法 ==========
     
     /**
      * 分页查询同步数据
      *
      * @param offset 偏移量
      * @param limit 限制数量
      * @return 同步数据列表
      */
     public List<BdVisitPlanIndicatorSync> selectSyncByPage(int offset, int limit) {
         try {
             List<BdVisitPlanIndicatorSync> result = bdVisitPlanIndicatorSyncMapper.selectByPage(offset, limit);
             log.debug("分页查询同步数据，offset: {}, limit: {}, 结果数量: {}", 
                     offset, limit, result != null ? result.size() : 0);
             return result;
         } catch (Exception e) {
             log.error("分页查询同步数据失败，offset: {}, limit: {}", offset, limit, e);
             throw e;
         }
     }
     
     /**
      * 分页查询去重门店ID列表
      *
      * @param offset 偏移量
      * @param limit 限制数量
      * @return 去重门店ID列表
      */
     public List<Long> selectDistinctMerchantIdsByPage(int offset, int limit) {
         try {
             List<Long> result = bdVisitPlanIndicatorSyncMapper.selectDistinctMerchantIdsByPage(offset, limit);
             log.debug("分页查询去重门店ID，offset: {}, limit: {}, 结果数量: {}", 
                     offset, limit, result != null ? result.size() : 0);
             return result;
         } catch (Exception e) {
             log.error("分页查询去重门店ID失败，offset: {}, limit: {}", offset, limit, e);
             throw e;
         }
     }
     
     /**
       * 根据门店ID列表查询同步数据
       *
       * @param merchantIds 门店ID列表
       * @return 同步数据列表
       */
      public List<BdVisitPlanIndicatorSync> selectSyncByMerchantIds(List<Long> merchantIds) {
          if (CollectionUtils.isEmpty(merchantIds)) {
              log.warn("根据门店ID查询同步数据：门店ID列表为空");
              return null;
          }
          
          try {
              List<BdVisitPlanIndicatorSync> result = bdVisitPlanIndicatorSyncMapper.selectByMerchantIds(merchantIds);
              log.debug("根据门店ID查询同步数据，门店数量: {}, 结果数量: {}", 
                      merchantIds.size(), result != null ? result.size() : 0);
              return result;
          } catch (Exception e) {
              log.error("根据门店ID查询同步数据失败，门店数量: {}", merchantIds.size(), e);
              throw e;
          }
      }
      

     
     /**
      * 插入同步数据
      *
      * @param record 同步数据记录
      * @return 影响行数
      */
     public int insertSync(BdVisitPlanIndicatorSync record) {
         if (record == null) {
             log.warn("插入同步数据：记录为空");
             return 0;
         }
         
         try {
             int rows = bdVisitPlanIndicatorSyncMapper.insert(record);
             log.debug("插入同步数据，影响行数: {}", rows);
             return rows;
         } catch (Exception e) {
             log.error("插入同步数据失败", e);
             throw e;
         }
     }
     
     /**
      * 选择性插入同步数据
      *
      * @param record 同步数据记录
      * @return 影响行数
      */
     public int insertSyncSelective(BdVisitPlanIndicatorSync record) {
         if (record == null) {
             log.warn("选择性插入同步数据：记录为空");
             return 0;
         }
         
         try {
             int rows = bdVisitPlanIndicatorSyncMapper.insert(record);
             log.debug("选择性插入同步数据，影响行数: {}", rows);
             return rows;
         } catch (Exception e) {
             log.error("选择性插入同步数据失败", e);
             throw e;
         }
     }
     
     /**
      * 批量插入同步数据
      *
      * @param records 同步数据记录列表
      * @return 影响行数
      */
     public int insertSyncBatch(List<BdVisitPlanIndicatorSync> records) {
         if (CollectionUtils.isEmpty(records)) {
             log.warn("批量插入同步数据：记录列表为空");
             return 0;
         }
         
         try {
             int rows = bdVisitPlanIndicatorSyncMapper.insertBatch(records);
             log.debug("批量插入同步数据，记录数量: {}, 影响行数: {}", records.size(), rows);
             return rows;
         } catch (Exception e) {
             log.error("批量插入同步数据失败，记录数量: {}", records.size(), e);
             throw e;
         }
     }
     
     /**
      * 根据主键查询同步数据
      *
      * @param id 主键ID
      * @return 同步数据记录
      */
     public BdVisitPlanIndicatorSync selectSyncByPrimaryKey(Long id) {
         if (id == null) {
             log.warn("根据主键查询同步数据：主键ID为空");
             return null;
         }
         
         try {
             BdVisitPlanIndicatorSync result = bdVisitPlanIndicatorSyncMapper.selectById(id);
             log.debug("根据主键查询同步数据，主键ID: {}, 结果: {}", id, result != null ? "找到" : "未找到");
             return result;
         } catch (Exception e) {
             log.error("根据主键查询同步数据失败，主键ID: {}", id, e);
             throw e;
         }
     }
     
     /**
       * 根据主键更新同步数据
       *
       * @param record 同步数据记录
       * @return 影响行数
       */
      public int updateSyncByPrimaryKey(BdVisitPlanIndicatorSync record) {
          if (record == null || record.getId() == null) {
              log.warn("根据主键更新同步数据：记录或主键ID为空");
              return 0;
          }
          
          try {
              int rows = bdVisitPlanIndicatorSyncMapper.updateById(record);
              log.debug("根据主键更新同步数据，主键ID: {}, 影响行数: {}", record.getId(), rows);
              return rows;
          } catch (Exception e) {
              log.error("根据主键更新同步数据失败，主键ID: {}", record.getId(), e);
              throw e;
          }
      }
      
      /**
       * 根据主键选择性更新同步数据
       *
       * @param record 同步数据记录
       * @return 影响行数
       */
      public int updateSyncByPrimaryKeySelective(BdVisitPlanIndicatorSync record) {
          if (record == null || record.getId() == null) {
              log.warn("根据主键选择性更新同步数据：记录或主键ID为空");
              return 0;
          }
          
          try {
              int rows = bdVisitPlanIndicatorSyncMapper.updateById(record);
              log.debug("根据主键选择性更新同步数据，主键ID: {}, 影响行数: {}", record.getId(), rows);
              return rows;
          } catch (Exception e) {
              log.error("根据主键选择性更新同步数据失败，主键ID: {}", record.getId(), e);
              throw e;
          }
      }
      
      /**
       * 根据主键删除同步数据
       *
       * @param id 主键ID
       * @return 影响行数
       */
      public int deleteSyncByPrimaryKey(Long id) {
          if (id == null) {
              log.warn("根据主键删除同步数据：主键ID为空");
              return 0;
          }
          
          try {
              int rows = bdVisitPlanIndicatorSyncMapper.deleteById(id);
              log.debug("根据主键删除同步数据，主键ID: {}, 影响行数: {}", id, rows);
              return rows;
          } catch (Exception e) {
              log.error("根据主键删除同步数据失败，主键ID: {}", id, e);
              throw e;
          }
      }
}