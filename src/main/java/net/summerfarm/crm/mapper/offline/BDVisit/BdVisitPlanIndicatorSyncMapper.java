package net.summerfarm.crm.mapper.offline.BDVisit;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import net.summerfarm.crm.model.domain.BDVisit.BdVisitPlanIndicatorSync;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 销售拜访计划指标同步表Mapper
 */
public interface BdVisitPlanIndicatorSyncMapper extends BaseMapper<BdVisitPlanIndicatorSync> {

    /**
     * 分页查询记录
     * @param offset 偏移量
     * @param limit 限制数量
     * @return 记录列表
     */
    List<BdVisitPlanIndicatorSync> selectByPage(@Param("offset") int offset, @Param("limit") int limit);

    /**
     * 根据门店ID列表查询记录
     * @param merchantIds 门店ID列表
     * @return 记录列表
     */
    List<BdVisitPlanIndicatorSync> selectByMerchantIds(@Param("merchantIds") List<Long> merchantIds);

    /**
     * 分页查询去重门店ID列表
     * @param offset 偏移量
     * @param limit 限制数量
     * @return 去重门店ID列表
     */
    List<Long> selectDistinctMerchantIdsByPage(@Param("offset") int offset, @Param("limit") int limit);

    /**
     * 批量插入记录
     * @param records 记录列表
     * @return 影响行数
     */
    int insertBatch(@Param("list") List<BdVisitPlanIndicatorSync> records);
}