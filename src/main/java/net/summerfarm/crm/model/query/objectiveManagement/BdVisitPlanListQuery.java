package net.summerfarm.crm.model.query.objectiveManagement;

import lombok.Data;
import net.summerfarm.crm.enums.SortDirectionEnum;

import javax.validation.constraints.Pattern;
import java.time.LocalDate;
import java.util.List;

/**
 * 销售拜访计划列表查询
 *
 * <AUTHOR>
 */
@Data
public class BdVisitPlanListQuery {

    /**
     * 拜访日期
     */
    private LocalDate visitDate;

    /**
     * 拜访类型，0：线下拜访，1：线上拜访
     */
    private Integer visitType;

    /**
     * 拜访状态，0：未拜访，1：已拜访
     */
    private Integer visitStatus;

    /**
     * 锁定状态，0：未锁定，1：已锁定
     */
    private Integer lockStatus;

    /**
     * 门店下单状态，0：拜访日未下单，1：拜访日已下单
     */
    private Integer merchantOrderStatus;

    /**
     * 拜访目标指标id列表
     */
    private List<Long> visitTargetIndicatorIds;

    /**
     * 排序字段：
     * 拜访价值 - potentialValue
     */
    @Pattern(regexp = "^(potentialValue)$", message = "排序字段必须是potentialValue")
    private String sortField;

    /**
     * 排序方向: 升序, 降序
     */
    private SortDirectionEnum sortDirection;
}
