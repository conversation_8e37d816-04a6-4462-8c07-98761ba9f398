package net.summerfarm.crm.model.bo.BDVisit;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 门店潜力值计算数据对象
 * 合并了BdVisitPlanIndicatorSync和BdVisitTargetIndicator的相关字段
 */
@Data
public class MerchantPotentialCalculationData {
    
    /**
     * 门店ID
     */
    private Long merchantId;
    
    /**
     * 拜访计划ID
     */
    private Long bdVisitPlanId;
    
    /**
     * 拜访目标指标ID
     */
    private Long bdVisitTargetIndicatorId;
    
    /**
     * 指标当前值
     */
    private BigDecimal indicatorCurrentValue;
    
    /**
     * 指标潜力值
     */
    private BigDecimal indicatorPotentialValue;
    
    /**
     * 目标指标名称
     */
    private String targetIndicatorName;
    
    /**
     * 目标指标权重
     */
    private BigDecimal targetIndicatorWeight;
    
    /**
     * 目标指标类型
     */
    private Integer targetIndicatorType;
    
    /**
     * 目标指标单位
     */
    private String targetIndicatorUnit;
    
    /**
     * 优先级, 1,2,3,4,5
     */
    private Integer priority;
    
    /**
     * 构造方法
     */
    public MerchantPotentialCalculationData() {
    }
    
    /**
     * 构造方法
     * @param merchantId 门店ID
     * @param bdVisitPlanId 拜访计划ID
     * @param bdVisitTargetIndicatorId 拜访目标指标ID
     * @param indicatorCurrentValue 指标当前值
     * @param indicatorPotentialValue 指标潜力值
     */
    public MerchantPotentialCalculationData(Long merchantId, Long bdVisitPlanId, 
                                          Long bdVisitTargetIndicatorId, 
                                          BigDecimal indicatorCurrentValue, 
                                          BigDecimal indicatorPotentialValue) {
        this.merchantId = merchantId;
        this.bdVisitPlanId = bdVisitPlanId;
        this.bdVisitTargetIndicatorId = bdVisitTargetIndicatorId;
        this.indicatorCurrentValue = indicatorCurrentValue;
        this.indicatorPotentialValue = indicatorPotentialValue;
    }
}