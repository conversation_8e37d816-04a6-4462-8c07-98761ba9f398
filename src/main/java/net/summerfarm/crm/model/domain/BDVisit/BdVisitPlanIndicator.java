package net.summerfarm.crm.model.domain.BDVisit;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * crm_bd_visit_plan_indicator
 * 销售拜访计划指标
 * <AUTHOR>
@Data
public class BdVisitPlanIndicator implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    private Long id;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 销售拜访计划id
     */
    private Long bdVisitPlanId;

    /**
     * 销售拜访目标id
     */
    private Long bdVisitTargetId;

    /**
     * 销售拜访目标指标id
     */
    private Long bdVisitTargetIndicatorId;

    /**
     * 销售id
     */
    private Integer bdId;

    /**
     * 门店id
     */
    private Long mId;

    /**
     * 指标名称
     */
    private String indicatorName;

    /**
     * 指标当前值
     */
    private BigDecimal indicatorCurrentValue;

    /**
     * 指标潜力值
     */
    private BigDecimal indicatorPotentialValue;
}