package net.summerfarm.crm.model.domain.BDVisit;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * crm_bd_visit_target_indicator_config
 * 销售拜访目标指标配置
 * <AUTHOR>
@Data
public class BdVisitTargetIndicatorConfig implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    private Long id;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 指标类型
     */
    private Integer indicatorType;

    /**
     * 指标名称
     */
    private String indicatorName;

    /**
     * 业务类型配置
     */
    private String businessTypeConfig;

    /**
     * 度量方式配置
     */
    private String metricsMethodConfig;

    /**
     * 品类名称配置
     */
    private String categoryNameConfig;
}