package net.summerfarm.crm.model.domain.BDVisit;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * crm_bd_visit_target
 * 销售拜访目标
 * <AUTHOR>
@Data
public class BdVisitTarget implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    private Long id;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 主管拜访目标id
     */
    private Long cityManagerVisitTargetId;

    /**
     * 销售id
     */
    private Integer bdId;

    /**
     * 销售名称
     */
    private String bdName;

    /**
     * 目标日期
     */
    private LocalDate targetDate;

    /**
     * 目标制定人id
     */
    private Integer targetCreator;

    /**
     * 目标制定人名称
     */
    private String targetCreatorName;
}