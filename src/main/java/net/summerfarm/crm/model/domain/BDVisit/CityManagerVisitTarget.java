package net.summerfarm.crm.model.domain.BDVisit;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * crm_city_manager_visit_target
 * 主管销售拜访目标
 * <AUTHOR>
@Data
public class CityManagerVisitTarget implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    private Long id;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 目标制定人id
     */
    private Integer targetCreator;

    /**
     * 目标制定人名称
     */
    private String targetCreatorName;

    /**
     * 销售名称列表，以逗号分隔
     */
    private String bdNameList;

    /**
     * 目标日期范围，以逗号分隔起止日期
     */
    private String targetDateRange;
}