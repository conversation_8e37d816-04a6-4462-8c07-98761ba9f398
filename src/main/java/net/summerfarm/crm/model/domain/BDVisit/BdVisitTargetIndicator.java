package net.summerfarm.crm.model.domain.BDVisit;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * crm_bd_visit_target_indicator
 * 销售拜访目标指标
 * <AUTHOR>
@Data
public class BdVisitTargetIndicator implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    private Long id;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 主管拜访目标id
     */
    private Long cityManagerVisitTargetId;

    /**
     * 销售拜访目标id
     */
    private Long bdVisitTargetId;

    /**
     * 销售id
     */
    private Integer bdId;

    /**
     * 销售名称
     */
    private String bdName;

    /**
     * 优先级, 1,2,3,4,5
     */
    private Integer priority;

    /**
     * 指标类型，1：拉新客户数，2：月活客户数，3：高价值客户数，4：平台总目标，5：品类目标，6：sku目标
     */
    private Integer indicatorType;

    /**
     * 指标名称
     */
    private String indicatorName;

    /**
     * 目标日期
     */
    private LocalDate targetDate;

    /**
     * 业务类型
     */
    private Integer businessType;

    /**
     * 度量方式
     */
    private Integer metricsMethod;

    /**
     * 品类名称
     */
    private String categoryName;

    /**
     * SKU编码
     */
    private String sku;

    /**
     * 指标期望值
     */
    private BigDecimal indicatorExpectedValue;

    /**
     * 指标状态，0：未完成，1：已完成
     */
    private Integer indicatorStatus;

    /**
     * 指标当前值
     */
    private BigDecimal indicatorCurrentValue;
}