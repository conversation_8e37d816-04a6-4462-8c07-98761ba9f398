package net.summerfarm.crm.model.entity.BDVisit;

import net.summerfarm.crm.model.domain.BDVisit.BdVisitPlanIndicator;

/**
 * BdVisitPlanIndicator查询对象
 */
public class BdVisitPlanIndicatorQuery extends BdVisitPlanIndicator {

    /**
     * 无参构造方法
     */
    public BdVisitPlanIndicatorQuery() {
        super();
    }

    /**
     * 带id参数的构造方法
     * @param id 主键ID
     */
    public BdVisitPlanIndicatorQuery(Long id) {
        super();
        this.setId(id);
    }
}