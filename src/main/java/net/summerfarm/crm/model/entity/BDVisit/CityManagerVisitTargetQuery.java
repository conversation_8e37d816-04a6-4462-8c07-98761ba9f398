package net.summerfarm.crm.model.entity.BDVisit;

import net.summerfarm.crm.model.domain.BDVisit.CityManagerVisitTarget;

/**
 * CityManagerVisitTarget查询对象
 */
public class CityManagerVisitTargetQuery extends CityManagerVisitTarget {

    /**
     * 无参构造方法
     */
    public CityManagerVisitTargetQuery() {
        super();
    }

    /**
     * 带id参数的构造方法
     * @param id 主键ID
     */
    public CityManagerVisitTargetQuery(Long id) {
        super();
        this.setId(id);
    }
}