package net.summerfarm.crm.model.entity.BDVisit;

import net.summerfarm.crm.model.domain.BDVisit.BdVisitPlan;

/**
 * BdVisitPlan查询对象
 */
public class BdVisitPlanQuery extends BdVisitPlan {

    /**
     * 无参构造方法
     */
    public BdVisitPlanQuery() {
        super();
    }

    /**
     * 带id参数的构造方法
     * @param id 主键ID
     */
    public BdVisitPlanQuery(Long id) {
        super();
        this.setId(id);
    }
}