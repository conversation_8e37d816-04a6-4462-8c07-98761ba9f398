package net.summerfarm.crm.model.entity.BDVisit;

import net.summerfarm.crm.model.domain.BDVisit.BdVisitTargetIndicatorConfig;

/**
 * BdVisitTargetIndicatorConfig查询对象
 */
public class BdVisitTargetIndicatorConfigQuery extends BdVisitTargetIndicatorConfig {

    /**
     * 无参构造方法
     */
    public BdVisitTargetIndicatorConfigQuery() {
        super();
    }

    /**
     * 带id参数的构造方法
     * @param id 主键ID
     */
    public BdVisitTargetIndicatorConfigQuery(Long id) {
        super();
        this.setId(id);
    }
}