package net.summerfarm.crm.model.entity.BDVisit;

import net.summerfarm.crm.model.domain.BDVisit.BdVisitTargetIndicator;

/**
 * BdVisitTargetIndicator查询对象
 */
public class BdVisitTargetIndicatorQuery extends BdVisitTargetIndicator {

    /**
     * 无参构造方法
     */
    public BdVisitTargetIndicatorQuery() {
        super();
    }

    /**
     * 带id参数的构造方法
     * @param id 主键ID
     */
    public BdVisitTargetIndicatorQuery(Long id) {
        super();
        this.setId(id);
    }
}