package net.summerfarm.crm.model.entity.BDVisit;

import net.summerfarm.crm.model.domain.BDVisit.BdVisitTarget;

/**
 * BdVisitTarget查询对象
 */
public class BdVisitTargetQuery extends BdVisitTarget {

    /**
     * 无参构造方法
     */
    public BdVisitTargetQuery() {
        super();
    }

    /**
     * 带id参数的构造方法
     * @param id 主键ID
     */
    public BdVisitTargetQuery(Long id) {
        super();
        this.setId(id);
    }
}