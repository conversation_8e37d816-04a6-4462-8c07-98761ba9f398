package net.summerfarm.crm.model.input.BDVisit;

import lombok.Data;
import net.summerfarm.crm.model.input.BDVisit.VisitMerchantInfoInput;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 生成销售拜访计划草案输入参数
 *
 * <AUTHOR>
 * @date 2025/09/04
 */
@Data
public class GenerateVisitPlanDraftInput {

    /**
     * 线上拜访门店列表
     */
    private List<VisitMerchantInfoInput> visitOnlineMIdList;

    /**
     * 线下拜访门店列表
     */
    private List<VisitMerchantInfoInput> visitOfflineMIdList;


     
    /**
     * 线下拜访数量, 大于1，小于200
     */
    @NotNull(message = "线下拜访数量不能为空")
    @Max(value = 200, message = "线下拜访数量不能超过200")
    @Min(value = 1, message = "线下拜访数量不能小于1")
    private Integer visitOfflineCount;

    /**
     * 线上拜访数量, 大于1，小于200
     */
    @NotNull(message = "线上拜访数量不能为空")
    @Max(value = 200, message = "线上拜访数量不能超过200")
    @Min(value = 1, message = "线上拜访数量不能小于1")
    private Integer visitOnlineCount;

    /**
     * 交通方式, 1-步行, 2-骑行 3-驾车，4-公交，5-地铁
     */
    @NotNull(message = "交通方式不能为空")
    private Integer trafficType;

    /**
     * 销售id
     */
    private Integer bdId;

}
