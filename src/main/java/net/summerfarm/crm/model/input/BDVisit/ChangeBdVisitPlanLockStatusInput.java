package net.summerfarm.crm.model.input.BDVisit;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.time.LocalDate;

/**
 * 修改销售拜访计划锁定状态的输入参数
 *
 * <AUTHOR>
 */
@Data
public class ChangeBdVisitPlanLockStatusInput {

    /**
     * 客户id
     */
    @NotNull(message = "客户id不能为空")
    private Long mId;

    /**
     * 拜访日期
     */
    private LocalDate visitDate;

    /**
     * 修改后的锁定状态
     */
    @NotNull(message = "修改后的锁定状态不能为空")
    private Integer destLockStatus;
}
