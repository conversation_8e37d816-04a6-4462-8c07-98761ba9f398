package net.summerfarm.crm.model.input.BDVisit;

import lombok.Data;
import net.summerfarm.crm.model.vo.PoiVO;

import java.util.List;

import javax.validation.constraints.NotNull;

/**
 * 重新生成销售拜访计划草案输入参数
 *
 * <AUTHOR>
 * @date 2025/09/04
 */
@Data
public class ReGenerateVisitPlanInput {

    /**
     * 省份
     */
    private String province;

    /**
     * 城市
     */
    private String city;

    /**
     * 区县
     */
    private String area;

    /**
     * poi位置信息
     */
    @NotNull(message = "poi位置信息不能为空")
    private PoiVO poi;

    /**
     * 需替换的线下客户列表
     */
    @NotNull(message = "需替换的线下客户列表不能为空")
    private List<VisitMerchantInfoInput> visitOfflineMIdList;

}
