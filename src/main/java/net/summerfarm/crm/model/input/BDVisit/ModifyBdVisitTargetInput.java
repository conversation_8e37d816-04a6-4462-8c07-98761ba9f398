package net.summerfarm.crm.model.input.BDVisit;

import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * 修改销售拜访目标的输入参数
 *
 * <AUTHOR>
 */
@Data
public class ModifyBdVisitTargetInput {

    /**
     * 销售拜访目标id
     */
    private Long id;

    /**
     * 替换的销售拜访目标指标列表，该列表会替换掉原来所有的销售拜访目标指标
     */
    @Valid
    @NotEmpty(message = "替换的销售拜访目标指标列表不能为空")
    private List<ReplaceBdVisitTargetIndicatorInput> replaceBdVisitTargetIndicators;
}
