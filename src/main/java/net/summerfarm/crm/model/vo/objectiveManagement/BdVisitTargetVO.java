package net.summerfarm.crm.model.vo.objectiveManagement;

import lombok.Data;
import java.time.LocalDate;
import java.util.List;

/**
 * 销售拜访目标信息
 * 
 * <AUTHOR>
 */
@Data
public class BdVisitTargetVO {

    /**
     * 销售ID
     */
    private Integer bdId;
    /**
     * 销售名称
     */
    private String bdName;

    /**
     * 目标制定人id
     */
    private Integer targetCreator;

    /**
     * 目标制定人名称
     */
    private String targetCreatorName;

    /**
     * 目标日期
     */
    private LocalDate targetDate;

    /**
     * 拜访目标指标列表
     */
    private List<BdVisitTargetIndicatorVO> visitTargetIndicators;

}
