# SummerFarm CRM 项目开发规范

## 项目概述

本项目是基于 Spring Boot 2.2.2 的 CRM 系统，采用 Maven 构建，使用 MyBatis 作为 ORM 框架，集成了 Elasticsearch、Redis、Nacos 等中间件。

## 技术栈

- **框架**: Spring Boot 2.2.2
- **构建工具**: Maven
- **数据库**: MySQL 8.0.23
- **ORM**: MyBatis 3.5.0
- **连接池**: Druid 1.2.6
- **缓存**: Redis 3.1.0
- **搜索引擎**: Elasticsearch 7.14.0
- **配置中心**: Nacos
- **RPC**: Dubbo
- **HTTP客户端**: OpenFeign
- **文档**: Swagger 2.7.0
- **工具库**: Lombok, FastJSON, Apache POI

## 项目结构规范

### 接口调用层级

项目采用分层架构，接口调用层级如下：

```
接入层 -> 业务层 -> 仓储层 -> 数据访问层
  ↓        ↓        ↓         ↓
Controller  Service  Repository  Mapper
MQ Listener    ↓        ↓         ↓
Task Handler   ↓        ↓         ↓
              ↓        ↓         ↓
            业务逻辑   数据封装   SQL执行
```

**层级职责说明：**
- **接入层（Controller/MQ Listener/Task Handler）**: 处理外部请求、消息监听、定时任务触发
- **业务层（Service）**: 实现核心业务逻辑、事务管理、业务规则校验
- **仓储层（Repository）**: 数据访问抽象、复杂查询封装、数据转换
- **数据访问层（Mapper）**: 直接的数据库操作、SQL映射

**调用原则：**
1. 上层只能调用直接下层，不能跨层调用
2. 同层之间可以相互调用
3. 下层不能调用上层
4. Repository层是可选的，简单场景可以Service直接调用Mapper

### 包结构

```
net.summerfarm.crm/
├── aspect/           # 切面类
├── common/           # 公共组件
│   ├── aspect/       # 公共切面
│   ├── base/         # 基础类
│   ├── config/       # 配置类
│   ├── constant/     # 常量定义
│   ├── exception/    # 异常处理
│   ├── interceptor/  # 拦截器
│   ├── redis/        # Redis相关
│   └── util/         # 工具类
├── controller/       # 控制器层（接入层）
├── entity/           # 实体类
│   └── manage/       # 管理模块实体
├── enums/            # 枚举类
├── es/               # Elasticsearch相关
│   ├── mapper/       # ES映射器
│   └── model/        # ES模型
├── facade/           # 外观层
├── factory/          # 工厂类
├── feign/            # Feign客户端
├── mapper/           # 数据访问层
│   ├── manage/       # 管理模块映射器
│   ├── offline/      # 离线数据映射器
│   └── repository/   # 仓储层
├── model/            # 模型类
│   ├── bo/           # 业务对象
│   ├── convert/      # 转换器
│   ├── domain/       # 领域对象
│   ├── dto/          # 数据传输对象
│   ├── input/        # 输入对象
│   ├── query/        # 查询对象
│   └── vo/           # 视图对象
├── mq/               # 消息队列（接入层）
├── provider/         # 服务提供者
├── service/          # 服务层（业务层）
└── task/             # 定时任务（接入层）
```

### 资源文件结构

```
src/main/resources/
├── application*.yml          # 配置文件
├── logback-spring.xml       # 日志配置
├── smart-doc.json           # 文档配置
├── excel/                   # Excel模板
├── img/                     # 图片资源
├── wecom/                   # 企微相关
└── net/summerfarm/crm/mapper/  # MyBatis映射文件
    ├── manage/              # 管理模块映射
    └── offline/             # 离线数据映射
```

## 编码规范

### 1. 命名规范

#### 类命名
- **实体类**: 使用名词，如 `CityManagerVisitTarget`
- **控制器**: 以 `Controller` 结尾，如 `VisitPlanController`
- **服务类**: 以 `Service` 结尾，如 `VisitPlanService`
- **映射器**: 以 `Mapper` 结尾，如 `BdVisitTargetMapper`
- **查询对象**: 以 `Query` 结尾，如 `BdVisitTargetQuery`
- **更新对象**: 以 `Update` 结尾，如 `BdVisitTargetUpdate`
- **枚举类**: 以 `Enum` 结尾，如 `VisitPlanEnum`

#### 枚举规范
- **枚举值命名**: 使用大写字母和下划线，如 `INCOMPLETE`, `COMPLETED`
- **枚举字段**: 必须包含 `code`（Integer类型）和 `desc`（String类型）字段
- **注释风格**: 使用单行注释 `//` 描述枚举值含义
- **构造方法**: 提供带参数的私有构造方法
- **访问方法**: 提供 `getCode()` 和 `getDesc()` 方法

**枚举示例**:
```java
public enum StatusEnum {
    // 未完成
    INCOMPLETE(0, "未完成"),
    // 已完成
    COMPLETED(1, "已完成");

    private final Integer code;
    private final String desc;

    StatusEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
```

#### 方法命名
- **查询方法**: `selectByXxx`, `findByXxx`, `getByXxx`, `list`
- **插入方法**: `insert`, `insertSelective`, `insertBatch`
- **更新方法**: `updateByXxx`, `updateByPrimaryKeySelective`
- **删除方法**: `deleteByXxx`, `deleteByPrimaryKey`

#### 字段命名
- 数据库字段使用下划线命名：`visit_plan_id`, `create_time`
- Java字段使用驼峰命名：`visitPlanId`, `createTime`

### 2. 实体类规范

#### 基础实体类
- 所有实体类应包含基础字段：`id`, `creator`, `createTime`, `updateTime`
- 使用 `@Data` 注解生成 getter/setter
- 主键使用 `@Id` 注解
- 字段使用 `@Column` 注解指定数据库字段名

#### Query对象规范
- 继承对应的实体类
- 仅包含无参构造方法和带id参数的构造方法
- 不添加额外的查询字段（保持简洁）

#### Update对象规范
- 继承对应的实体类
- 所有字段都是可选的，用于选择性更新

### 3. Mapper接口规范

#### 标准方法
每个Mapper接口应包含以下标准方法：
```java
// 基础CRUD
int deleteByPrimaryKey(Long id);
int insert(EntityName record);
int insertSelective(EntityName record);
EntityName selectByPrimaryKey(Long id);
int updateByPrimaryKeySelective(EntityNameUpdate record);

// 批量操作
int insertBatch(List<EntityName> records);

// 查询列表
List<EntityName> list(EntityNameQuery query);
```

### 4. XML映射文件规范

#### 文件结构
```xml
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="完整的Mapper接口路径">
    <!-- 基础结果映射 -->
    <resultMap id="BaseResultMap" type="实体类完整路径">
        <!-- 字段映射 -->
    </resultMap>
    
    <!-- 基础字段列表 -->
    <sql id="Base_Column_List">
        <!-- 所有字段 -->
    </sql>
    
    <!-- 标准CRUD方法 -->
    <!-- 查询方法 -->
    <!-- 批量操作方法 -->
</mapper>
```

#### SQL规范
- 使用 `<if test="condition">` 进行条件判断
- 字符串条件需要检查非空：`test="field != null and field != ''"`
- 查询结果按 `id DESC` 排序
- 使用 `<include refid="Base_Column_List" />` 引用字段列表

### 5. 服务层规范

#### 事务管理
- 使用 `@Transactional` 注解管理事务
- 只读操作使用 `@Transactional(readOnly = true)`

#### 异常处理
- 使用统一的异常处理机制
- 自定义业务异常继承 `RuntimeException`

### 6. 控制器规范

#### 注解使用
- 类级别使用 `@RestController`
- 方法级别使用 `@GetMapping`, `@PostMapping` 等
- 使用 `@RequestBody`, `@RequestParam`, `@PathVariable` 接收参数

#### 返回值规范
- 统一使用 `Result<T>` 包装返回结果
- 成功：`Result.success(data)`
- 失败：`Result.error(message)`

## 数据库规范

### 表命名
- 使用小写字母和下划线
- 以模块前缀开头，如 `crm_bd_visit_target`

### 字段规范
- 主键：`id BIGINT AUTO_INCREMENT PRIMARY KEY`
- 创建人：`creator VARCHAR(50)`
- 创建时间：`create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP`
- 更新时间：`update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP`

## 配置规范

### 应用配置
- 使用 YAML 格式
- 按环境分离：`application-{env}.yml`
- 敏感信息使用 Nacos 配置中心

### MyBatis配置
- 映射文件放在 `resources/net/summerfarm/crm/mapper/` 下
- 使用驼峰命名转换：`mapUnderscoreToCamelCase: true`

## 开发流程

### 1. 新增功能开发
1. 创建数据库表
2. 生成实体类、Query对象、Update对象
3. 创建Mapper接口和XML映射文件
4. 实现Service层业务逻辑
5. 创建Controller接口
6. 编写单元测试

### 2. 代码提交规范
- 提交信息格式：`[类型] 简短描述`
- 类型：feat(新功能)、fix(修复)、docs(文档)、style(格式)、refactor(重构)、test(测试)

## 注意事项

1. **性能优化**
   - 避免 N+1 查询问题
   - 合理使用索引
   - 大数据量查询使用分页

2. **安全规范**
   - 所有外部输入都要进行校验
   - 使用参数化查询防止SQL注入
   - 敏感信息不要记录到日志

3. **代码质量**
   - 保持方法简洁，单一职责
   - 适当添加注释说明复杂逻辑
   - 遵循阿里巴巴Java开发手册

4. **测试规范**
   - 核心业务逻辑必须有单元测试
   - 测试覆盖率不低于70%
   - 集成测试覆盖主要业务流程

## 工具推荐

- **IDE**: IntelliJ IDEA
- **插件**: Lombok, MyBatis Log Plugin, Alibaba Java Coding Guidelines
- **数据库工具**: Navicat, DataGrip
- **API测试**: Postman, Swagger UI
- **版本控制**: Git

---

本规范会根据项目发展持续更新，请开发团队严格遵守以确保代码质量和项目的可维护性。